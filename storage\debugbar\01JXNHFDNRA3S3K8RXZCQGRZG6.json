{"__meta": {"id": "01JXNHFDNRA3S3K8RXZCQGRZG6", "datetime": "2025-06-13 21:00:05", "utime": **********.689297, "method": "GET", "uri": "/pro/update-csv", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.110813, "end": **********.689311, "duration": 245.5784981250763, "duration_str": "245.58s", "measures": [{"label": "Booting", "start": **********.110813, "relative_start": 0, "end": **********.84007, "relative_end": **********.84007, "duration": 0.****************, "duration_str": "729ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.840083, "relative_start": 0.****************, "end": **********.689314, "relative_end": 2.86102294921875e-06, "duration": 244.**************, "duration_str": "244.85s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.861498, "relative_start": 0.****************, "end": **********.868266, "relative_end": **********.868266, "duration": 0.006767988204956055, "duration_str": "6.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.685291, "relative_start": 245.**************, "end": **********.686282, "relative_end": **********.686282, "duration": 0.0009908676147460938, "duration_str": "991μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 500, "nb_statements": 42292, "nb_visible_statements": 500, "nb_excluded_statements": 41792, "nb_failed_statements": 0, "accumulated_duration": 1.4578999999999995, "accumulated_duration_str": "1.46s", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft and hard limit for Debugbar are reached. Only the first 100 queries show details. Queries after the first 500 are ignored. Limits can be raised in the config (debugbar.options.db.soft/hard_limit).", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": null, "name": "platform/plugins/ecommerce/src/Services/Footprints/TrackingFilter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Services\\Footprints\\TrackingFilter.php", "line": 45}], "start": **********.88015, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = 'HYUDRAULIC PUMP' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["HYUDRAULIC PUMP", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.888955, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.036, "width_percent": 0.043}, {"sql": "select * from `mp_stores` where `name` = '' limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.8909929, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.079, "width_percent": 0.026}, {"sql": "update `ec_products` set `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:00' where `id` = 87", "type": "query", "params": [], "bindings": [{"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:00", 87], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.8949108, "duration": 0.039060000000000004, "duration_str": "39.06ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 0.105, "width_percent": 2.679}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 87 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [87], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.938148, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 2.784, "width_percent": 0.047}, {"sql": "select * from `ec_products` where `name` = 'COOLANT PUMP REAR HOUSING (********)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["COOLANT PUMP REAR HOUSING (********)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.952358, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.831, "width_percent": 0.119}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.9575758, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.949, "width_percent": 0.054}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:00' where `id` = 88", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:00", 88], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.963477, "duration": 0.0035499999999999998, "duration_str": "3.55ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 3.004, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 88 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [88], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.969144, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 3.247, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = 'Hydraulic Main Pump for ZX350-5G Excavator: High-Pressure Axial Piston Pump Model Yb60000309' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Hydraulic Main Pump for ZX350-5G Excavator: High-Pressure Axial Piston Pump Model Yb60000309", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.974798, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.283, "width_percent": 0.147}, {"sql": "select * from `mp_stores` where `name` = 'HITACHI' limit 1", "type": "query", "params": [], "bindings": ["HITACHI"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.978467, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.43, "width_percent": 0.05}, {"sql": "update `ec_products` set `store_id` = 76, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:00' where `id` = 89", "type": "query", "params": [], "bindings": [76, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:00", 89], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.983593, "duration": 0.00312, "duration_str": "3.12ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 3.48, "width_percent": 0.214}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 89 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [89], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.991367, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 3.694, "width_percent": 0.056}, {"sql": "select * from `ec_products` where `name` = 'Inverter (Automatic Frequency Controller)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Inverter (Automatic Frequency Controller)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.996001, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.749, "width_percent": 0.21}, {"sql": "select * from `mp_stores` where `name` = 'DOOSAN' limit 1", "type": "query", "params": [], "bindings": ["DOOSAN"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.00054, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 3.959, "width_percent": 0.034}, {"sql": "update `ec_products` set `store_id` = 69, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 90", "type": "query", "params": [], "bindings": [69, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 90], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.005267, "duration": 0.00491, "duration_str": "4.91ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 3.993, "width_percent": 0.337}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 90 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [90], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0125132, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 4.33, "width_percent": 0.067}, {"sql": "select * from `ec_products` where `name` = 'Schneider Electric LC1D80U7' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric LC1D80U7", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.016237, "duration": 0.00216, "duration_str": "2.16ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.397, "width_percent": 0.148}, {"sql": "select * from `mp_stores` where `name` = '' limit 1", "type": "query", "params": [], "bindings": [""], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0196629, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.546, "width_percent": 0.028}, {"sql": "update `ec_products` set `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 91", "type": "query", "params": [], "bindings": [{"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 91], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.0234091, "duration": 0.00276, "duration_str": "2.76ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 4.574, "width_percent": 0.189}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 91 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [91], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.028056, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 4.763, "width_percent": 0.042}, {"sql": "select * from `ec_products` where `name` = 'Solar Powered Rotating Helicopter Car Perfume (Car Air Freshenerr)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Solar Powered Rotating Helicopter Car Perfume (Car Air Freshenerr)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.031549, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.805, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.034147, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 4.889, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'marine', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 92", "type": "query", "params": [], "bindings": [65, {"value": "marine", "label": "Marine"}, "2025-06-13 20:56:01", 92], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.038828, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 4.917, "width_percent": 0.149}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 92 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [92], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.043197, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 5.066, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = 'Volvo Excavator Radiator VOE******** ********' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Volvo Excavator Radiator VOE******** ********", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.046534, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.102, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.048882, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.174, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 93", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 93], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.053461, "duration": 0.00221, "duration_str": "2.21ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 5.198, "width_percent": 0.152}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 93 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [93], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.058592, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 5.349, "width_percent": 0.044}, {"sql": "select * from `ec_products` where `name` = 'RS PRO ISO Standard Cylinder Stroke, Double Acting  - ( 80mm Bore, 125mm )' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["RS PRO ISO Standard Cylinder Stroke, Double Acting  - ( 80mm Bore, 125mm )", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.062025, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.393, "width_percent": 0.11}, {"sql": "select * from `mp_stores` where `name` = 'RS Pro' limit 1", "type": "query", "params": [], "bindings": ["RS Pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.0649981, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.503, "width_percent": 0.057}, {"sql": "update `ec_products` set `store_id` = 87, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 94", "type": "query", "params": [], "bindings": [87, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 94], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.0700731, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 5.56, "width_percent": 0.226}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 94 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [94], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.075506, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 5.786, "width_percent": 0.062}, {"sql": "select * from `ec_products` where `name` = '5 Way 2 Position Solenoid Spring valve' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["5 Way 2 Position Solenoid Spring valve", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.082345, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.849, "width_percent": 0.083}, {"sql": "select * from `mp_stores` where `name` = 'RS Pro' limit 1", "type": "query", "params": [], "bindings": ["RS Pro"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.084921, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 5.932, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = 87, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 95", "type": "query", "params": [], "bindings": [87, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 95], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.089934, "duration": 0.00292, "duration_str": "2.92ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 5.959, "width_percent": 0.2}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 95 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [95], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.094939, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 6.159, "width_percent": 0.064}, {"sql": "select * from `ec_products` where `name` = 'TeSys, Contactor, TeSys Deca, 3P(3NO), AC-3/AC-3e, <=440V, 80A, 240V AC 50/60Hz coil' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["TeSys, Contactor, TeSys Deca, 3P(3NO), AC-3/AC-3e, <=440V, 80A, 240V AC 50/60Hz coil", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.098886, "duration": 0.0183, "duration_str": "18.3ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.223, "width_percent": 1.255}, {"sql": "select * from `ec_products` where `name` = 'OIL FILTER' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["OIL FILTER", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.120172, "duration": 0.00152, "duration_str": "1.52ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.479, "width_percent": 0.104}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1229851, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.583, "width_percent": 0.043}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 97", "type": "query", "params": [], "bindings": [82, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 97], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.1277802, "duration": 0.0023799999999999997, "duration_str": "2.38ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 7.626, "width_percent": 0.163}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 97 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [97], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.131918, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 7.789, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = 'Volvo15126069 Fuel Filter Spin-on for Volvo Dump Trucks, Excavators, Loaders' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Volvo15126069 Fuel Filter Spin-on for Volvo Dump Trucks, Excavators, Loaders", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1351888, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.822, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.137847, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 7.9, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 98", "type": "query", "params": [], "bindings": [82, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 98], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.143094, "duration": 0.01828, "duration_str": "18.28ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 7.929, "width_percent": 1.254}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 98 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [98], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.163495, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 9.182, "width_percent": 0.032}, {"sql": "select * from `ec_products` where `name` = 'Electronic pressure sensors (XMLP001GC21F)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Electronic pressure sensors (XMLP001GC21F)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.166884, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.214, "width_percent": 0.088}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1696088, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.302, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 99", "type": "query", "params": [], "bindings": [82, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 99], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.174376, "duration": 0.00745, "duration_str": "7.45ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 9.33, "width_percent": 0.511}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 99 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [99], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.1838322, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 9.841, "width_percent": 0.055}, {"sql": "select * from `ec_products` where `name` = 'JCB Hydraulic Pump 332/F9031' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["JCB Hydraulic Pump 332/F9031", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.187396, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.896, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.189764, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 9.966, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 100", "type": "query", "params": [], "bindings": [82, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 100], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.1943882, "duration": 0.00749, "duration_str": "7.49ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 9.992, "width_percent": 0.514}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 100 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [100], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.203956, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 10.506, "width_percent": 0.062}, {"sql": "select * from `ec_products` where `name` = 'Digital Hydraulic Multimeter Webtec DHM804- S-7-L Digital Unit 1-7/8” - 12UN Ports' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Digital Hydraulic Multimeter Webtec DHM804- S-7-L Digital Unit 1-7/8” - 12UN Ports", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.207849, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.568, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.210355, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.641, "width_percent": 0.036}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 101", "type": "query", "params": [], "bindings": [82, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 101], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.2159722, "duration": 0.0031, "duration_str": "3.1ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 10.678, "width_percent": 0.213}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 101 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [101], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2211912, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 10.89, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = '(1053196) Motor Group Fits Caterpillar 120G 120H 120H ES 120H NA 120K 120M 12G' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["(1053196) Motor Group Fits Caterpillar 120G 120H 120H ES 120H NA 120K 120M 12G", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.224587, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 10.923, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2272449, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.007, "width_percent": 0.039}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 102", "type": "query", "params": [], "bindings": [82, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 102], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.232207, "duration": 0.00767, "duration_str": "7.67ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 11.046, "width_percent": 0.526}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 102 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [102], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.242147, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 11.572, "width_percent": 0.041}, {"sql": "select * from `ec_products` where `name` = 'Genuine Inter-cooler Hose' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Genuine Inter-cooler Hose", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.245917, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.613, "width_percent": 0.13}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2495081, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 11.743, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'marine', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 103", "type": "query", "params": [], "bindings": [82, {"value": "marine", "label": "Marine"}, "2025-06-13 20:56:01", 103], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.254307, "duration": 0.00322, "duration_str": "3.22ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 11.775, "width_percent": 0.221}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 103 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [103], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.259707, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 11.996, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = 'CONE-ROLLER BEARING (DE2N2189)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["CONE-ROLLER BEARING (DE2N2189)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.263335, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.036, "width_percent": 0.101}, {"sql": "select * from `mp_stores` where `name` = 'Schneider Electric' limit 1", "type": "query", "params": [], "bindings": ["Schneider Electric"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2662098, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.137, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = 82, `main_type` = 'industrial', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 104", "type": "query", "params": [], "bindings": [82, {"value": "industrial", "label": "Industrial"}, "2025-06-13 20:56:01", 104], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.2708778, "duration": 0.00304, "duration_str": "3.04ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 12.165, "width_percent": 0.209}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 104 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [104], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.276461, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 12.374, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = 'Block Cylinder Assy 0497' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Block Cylinder Assy 0497", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2800012, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.414, "width_percent": 0.093}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2827601, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.507, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 105", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 105], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.287436, "duration": 0.00299, "duration_str": "2.99ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 12.539, "width_percent": 0.205}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 105 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [105], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2929008, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 12.744, "width_percent": 0.042}, {"sql": "select * from `ec_products` where `name` = 'Piston Hydraulic Pump 04574' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Piston Hydraulic Pump 04574", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.2962348, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.786, "width_percent": 0.162}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.300043, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 12.947, "width_percent": 0.033}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 106", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 106], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.304807, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 12.98, "width_percent": 0.22}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 106 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [106], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.3101351, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 13.2, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = 'Retainer Hydraulic Pump K3V180 (18272)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Retainer Hydraulic Pump K3V180 (18272)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.313627, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.236, "width_percent": 0.071}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.3159711, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.307, "width_percent": 0.022}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 107", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 107], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.3205, "duration": 0.00282, "duration_str": "2.82ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 13.329, "width_percent": 0.193}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 107 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [107], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.327666, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 13.522, "width_percent": 0.043}, {"sql": "select * from `ec_products` where `name` = 'Seal Kit Hydraulic Pump K3V180 (********)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Seal Kit Hydraulic Pump K3V180 (********)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.334078, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.565, "width_percent": 0.095}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.336864, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.66, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 108", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 108], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.34195, "duration": 0.00342, "duration_str": "3.42ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 13.683, "width_percent": 0.235}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 108 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [108], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.347404, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 13.917, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = 'Shoe Plate Hydraulic Pump Model K3V180 (18838)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Shoe Plate Hydraulic Pump Model K3V180 (18838)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.350621, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 13.951, "width_percent": 0.106}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.354366, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.057, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 109", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 109], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.359052, "duration": 0.0028399999999999996, "duration_str": "2.84ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 14.089, "width_percent": 0.195}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 109 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [109], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.363812, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 14.284, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = 'Spring Hydraulic Pump Model K3V180' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Spring Hydraulic Pump Model K3V180", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.366959, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.317, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.369498, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.391, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 110", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 110], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.3738449, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 14.417, "width_percent": 0.198}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 110 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [110], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.378684, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 14.614, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = 'Guide Hydraulic Pump Model K3V180 (04574)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Guide Hydraulic Pump Model K3V180 (04574)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.381785, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.647, "width_percent": 0.071}, {"sql": "select * from `mp_stores` where `name` = 'VOLVO' limit 1", "type": "query", "params": [], "bindings": ["VOLVO"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.3842509, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.718, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = 65, `main_type` = 'heavy-equipment', `ec_products`.`updated_at` = '2025-06-13 20:56:01' where `id` = 111", "type": "query", "params": [], "bindings": [65, {"value": "heavy-equipment", "label": "Heavy Equipment"}, "2025-06-13 20:56:01", 111], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 15, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.388748, "duration": 0.00447, "duration_str": "4.47ms", "memory": 0, "memory_str": null, "filename": "TempController.php:81", "source": {"index": 14, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=81", "ajax": false, "filename": "TempController.php", "line": "81"}, "connection": "muhrak", "explain": null, "start_percent": 14.744, "width_percent": 0.307}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = 111 and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [111], "hints": null, "show_copy": true, "backtrace": [{"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 81}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.395335, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "Product.php:148", "source": {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Product.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Product.php", "line": 148}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=148", "ajax": false, "filename": "Product.php", "line": "148"}, "connection": "muhrak", "explain": null, "start_percent": 15.05, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = 'Siemens (SQN70.244A20)' and (`ec_products`.`is_variation` = 1 or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = 'published')) limit 1", "type": "query", "params": [], "bindings": ["Siemens (SQN70.244A20)", 1, "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 57}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.39858, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.084, "width_percent": 0.107}, {"sql": "select * from `mp_stores` where `name` = 'Siemens' limit 1", "type": "query", "params": [], "bindings": ["Siemens"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 63}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/TempController.php", "file": "D:\\laragon\\www\\muhrak\\app\\Http\\Controllers\\TempController.php", "line": 33}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 47}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}], "start": **********.4015431, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 15.191, "width_percent": 0.034}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4061608, "duration": 0.00464, "duration_str": "4.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.225, "width_percent": 0.318}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.413706, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.543, "width_percent": 0.046}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.416681, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.589, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.418456, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.669, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4224138, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.693, "width_percent": 0.268}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.42904, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.961, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.432265, "duration": 0.02246, "duration_str": "22.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 15.996, "width_percent": 1.541}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.45639, "duration": 0.01797, "duration_str": "17.97ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 17.537, "width_percent": 1.233}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.4761322, "duration": 0.01752, "duration_str": "17.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 18.769, "width_percent": 1.202}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.495216, "duration": 0.01775, "duration_str": "17.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 19.971, "width_percent": 1.218}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5146449, "duration": 0.01776, "duration_str": "17.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 21.189, "width_percent": 1.218}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.533986, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.407, "width_percent": 0.167}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.537005, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.574, "width_percent": 0.034}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.541089, "duration": 0.00454, "duration_str": "4.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.609, "width_percent": 0.311}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.546775, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.92, "width_percent": 0.031}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.54925, "duration": 0.0019399999999999999, "duration_str": "1.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 22.951, "width_percent": 0.133}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5517309, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.084, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.555671, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.109, "width_percent": 0.237}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.561915, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.346, "width_percent": 0.06}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.564878, "duration": 0.01669, "duration_str": "16.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 23.406, "width_percent": 1.145}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.5832298, "duration": 0.018690000000000002, "duration_str": "18.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 24.551, "width_percent": 1.282}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.603584, "duration": 0.02199, "duration_str": "21.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 25.833, "width_percent": 1.508}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.627456, "duration": 0.02102, "duration_str": "21.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 27.341, "width_percent": 1.442}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.651714, "duration": 0.01787, "duration_str": "17.87ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 28.783, "width_percent": 1.226}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.671318, "duration": 0.029769999999999998, "duration_str": "29.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 30.009, "width_percent": 2.042}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.7041662, "duration": 0.01804, "duration_str": "18.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 32.051, "width_percent": 1.237}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.72533, "duration": 0.0221, "duration_str": "22.1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 33.288, "width_percent": 1.516}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.750809, "duration": 0.02454, "duration_str": "24.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 34.804, "width_percent": 1.683}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.778274, "duration": 0.02156, "duration_str": "21.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 36.487, "width_percent": 1.479}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.8015668, "duration": 0.01729, "duration_str": "17.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 37.966, "width_percent": 1.186}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.820457, "duration": 0.01831, "duration_str": "18.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 39.152, "width_percent": 1.256}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.840475, "duration": 0.0224, "duration_str": "22.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 40.408, "width_percent": 1.536}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.864463, "duration": 0.01819, "duration_str": "18.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 41.945, "width_percent": 1.248}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.885226, "duration": 0.027960000000000002, "duration_str": "27.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 43.192, "width_percent": 1.918}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9161131, "duration": 0.03245, "duration_str": "32.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 45.11, "width_percent": 2.226}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9510489, "duration": 0.02868, "duration_str": "28.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 47.336, "width_percent": 1.967}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.981409, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.303, "width_percent": 0.131}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9842548, "duration": 0.002, "duration_str": "2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.434, "width_percent": 0.137}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.989805, "duration": 0.00704, "duration_str": "7.04ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 49.571, "width_percent": 0.483}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9988148, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.054, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0018408, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.094, "width_percent": 0.101}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.003868, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.195, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0095422, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.217, "width_percent": 0.283}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.015339, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.501, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.019463, "duration": 0.00875, "duration_str": "8.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 50.541, "width_percent": 0.6}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0289798, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.141, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.03508, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.171, "width_percent": 0.324}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.041633, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.495, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0462441, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.535, "width_percent": 0.081}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.048024, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.616, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.054674, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.643, "width_percent": 0.292}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.060936, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.935, "width_percent": 0.049}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.066117, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 51.984, "width_percent": 0.134}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0686262, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.118, "width_percent": 0.021}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.072294, "duration": 0.00347, "duration_str": "3.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.139, "width_percent": 0.238}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.078498, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.377, "width_percent": 0.048}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.083795, "duration": 0.0013700000000000001, "duration_str": "1.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.425, "width_percent": 0.094}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.085685, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.519, "width_percent": 0.019}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.091066, "duration": 0.00405, "duration_str": "4.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.538, "width_percent": 0.278}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0963762, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.816, "width_percent": 0.031}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.0991678, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 52.847, "width_percent": 0.163}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.102142, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.009, "width_percent": 0.021}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.106825, "duration": 0.004200000000000001, "duration_str": "4.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.03, "width_percent": 0.288}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.1122298, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.318, "width_percent": 0.029}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.1161811, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.347, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.117891, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.426, "width_percent": 0.019}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.121562, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.445, "width_percent": 0.25}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.127042, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.696, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.1296139, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.73, "width_percent": 0.109}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.13173, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.839, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.135344, "duration": 0.0035600000000000002, "duration_str": "3.56ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 53.863, "width_percent": 0.244}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.140018, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.107, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.144087, "duration": 0.0016899999999999999, "duration_str": "1.69ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.14, "width_percent": 0.116}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.1464078, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.256, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.150333, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.282, "width_percent": 0.255}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.156308, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.537, "width_percent": 0.038}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.161206, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.575, "width_percent": 0.114}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.163555, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.689, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.167323, "duration": 0.00468, "duration_str": "4.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 54.713, "width_percent": 0.321}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.173412, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.034, "width_percent": 0.06}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.176567, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.094, "width_percent": 0.108}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.1794221, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.201, "width_percent": 0.031}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.184137, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.232, "width_percent": 0.261}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.189126, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.493, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.191806, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.528, "width_percent": 0.086}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.193593, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.614, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.197135, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.641, "width_percent": 0.264}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.202189, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.905, "width_percent": 0.042}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.205515, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 55.947, "width_percent": 0.099}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.207581, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.046, "width_percent": 0.03}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2115738, "duration": 0.00425, "duration_str": "4.25ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.077, "width_percent": 0.292}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.216984, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.368, "width_percent": 0.039}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.221788, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.407, "width_percent": 0.088}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.22369, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.495, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.230328, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.523, "width_percent": 0.274}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2369401, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.797, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.239521, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.832, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.241067, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.905, "width_percent": 0.022}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2450018, "duration": 0.00394, "duration_str": "3.94ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 56.927, "width_percent": 0.27}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.250111, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.197, "width_percent": 0.057}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.255142, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.254, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.256641, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.324, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2605472, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.348, "width_percent": 0.248}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2667072, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.596, "width_percent": 0.032}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.269175, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.628, "width_percent": 0.069}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.270658, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.697, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2742128, "duration": 0.0039, "duration_str": "3.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.72, "width_percent": 0.268}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.279231, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 57.988, "width_percent": 0.03}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.281765, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.018, "width_percent": 0.134}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2842999, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.152, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.287986, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.182, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2929862, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.448, "width_percent": 0.032}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2954738, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.48, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.2970989, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.558, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.300662, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.586, "width_percent": 0.242}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.305217, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.828, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.307844, "duration": 0.00163, "duration_str": "1.63ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.863, "width_percent": 0.112}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.310502, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 58.975, "width_percent": 0.035}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3148038, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.01, "width_percent": 0.259}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3211439, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.268, "width_percent": 0.038}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.323779, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.306, "width_percent": 0.082}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.325654, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.388, "width_percent": 0.038}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3328109, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.427, "width_percent": 0.254}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3389862, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.68, "width_percent": 0.045}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.344361, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.726, "width_percent": 0.091}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3462539, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.817, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.349928, "duration": 0.00412, "duration_str": "4.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 59.845, "width_percent": 0.283}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.355187, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.128, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3577359, "duration": 0.00181, "duration_str": "1.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.163, "width_percent": 0.124}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.360163, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.287, "width_percent": 0.034}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.364173, "duration": 0.0047, "duration_str": "4.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.321, "width_percent": 0.322}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.37011, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.643, "width_percent": 0.049}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.375805, "duration": 0.0030600000000000002, "duration_str": "3.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.692, "width_percent": 0.21}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3795462, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.902, "width_percent": 0.031}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.383552, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 60.933, "width_percent": 0.279}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.38882, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.212, "width_percent": 0.038}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.391978, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.25, "width_percent": 0.103}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.3945441, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.353, "width_percent": 0.035}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.403295, "duration": 0.00426, "duration_str": "4.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.388, "width_percent": 0.292}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.410272, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.68, "width_percent": 0.042}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.415447, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.722, "width_percent": 0.098}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.417498, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.82, "width_percent": 0.03}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.4213178, "duration": 0.00429, "duration_str": "4.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 61.85, "width_percent": 0.294}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.426898, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.144, "width_percent": 0.043}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.430832, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.187, "width_percent": 0.089}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.432678, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.277, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.436548, "duration": 0.039810000000000005, "duration_str": "39.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 62.301, "width_percent": 2.731}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.477626, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.032, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.480375, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.065, "width_percent": 0.075}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.481984, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.14, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.485908, "duration": 0.05559, "duration_str": "55.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 65.166, "width_percent": 3.813}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.542843, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 68.979, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.545799, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.012, "width_percent": 0.079}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.547503, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.09, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.5536401, "duration": 0.0055899999999999995, "duration_str": "5.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.117, "width_percent": 0.383}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.567989, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.501, "width_percent": 0.119}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.578359, "duration": 0.00372, "duration_str": "3.72ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.62, "width_percent": 0.255}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.5831509, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.875, "width_percent": 0.052}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.590001, "duration": 0.00419, "duration_str": "4.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 69.927, "width_percent": 0.287}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.596174, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.215, "width_percent": 0.052}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.6005251, "duration": 0.00188, "duration_str": "1.88ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.267, "width_percent": 0.129}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.603333, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.396, "width_percent": 0.043}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.610354, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.439, "width_percent": 0.268}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.6153839, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.707, "width_percent": 0.038}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.618182, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.746, "width_percent": 0.119}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.6205451, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.864, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.624245, "duration": 0.00395, "duration_str": "3.95ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 70.896, "width_percent": 0.271}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.629304, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.167, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.631975, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.207, "width_percent": 0.12}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.634254, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.327, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.638073, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.356, "width_percent": 0.261}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.643148, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.617, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.6459188, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.652, "width_percent": 0.087}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.647731, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.739, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.651459, "duration": 0.0037099999999999998, "duration_str": "3.71ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 71.771, "width_percent": 0.254}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.656537, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.026, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.659312, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.06, "width_percent": 0.083}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.661074, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.143, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.664711, "duration": 0.00364, "duration_str": "3.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.17, "width_percent": 0.25}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.669378, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.42, "width_percent": 0.037}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.672154, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.457, "width_percent": 0.086}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.6739569, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.543, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.677826, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.573, "width_percent": 0.252}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.682587, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.825, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.685091, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.86, "width_percent": 0.076}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.686927, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.936, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.690538, "duration": 0.004019999999999999, "duration_str": "4.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 72.962, "width_percent": 0.276}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.695715, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.238, "width_percent": 0.039}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.698476, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.277, "width_percent": 0.108}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.700674, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.385, "width_percent": 0.034}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.704526, "duration": 0.0149, "duration_str": "14.9ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 73.419, "width_percent": 1.022}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.720682, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.441, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.723342, "duration": 0.00647, "duration_str": "6.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.477, "width_percent": 0.444}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.7303998, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.921, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.734299, "duration": 0.04764, "duration_str": "47.64ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 74.948, "width_percent": 3.268}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.7845602, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.215, "width_percent": 0.044}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.787399, "duration": 0.01702, "duration_str": "17.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 78.259, "width_percent": 1.167}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.805059, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.427, "width_percent": 0.035}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.808911, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.462, "width_percent": 0.265}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.813972, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.726, "width_percent": 0.038}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.816541, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.764, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.818296, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.848, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.822052, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 79.875, "width_percent": 0.283}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.828781, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.158, "width_percent": 0.042}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.831494, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.2, "width_percent": 0.069}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.832958, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.269, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8363361, "duration": 0.00385, "duration_str": "3.85ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.293, "width_percent": 0.264}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.841371, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.557, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8441179, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.591, "width_percent": 0.088}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8459768, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.68, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.849656, "duration": 0.00382, "duration_str": "3.82ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.707, "width_percent": 0.262}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.854697, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 80.969, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.857194, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.003, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.859068, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.076, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.862564, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.103, "width_percent": 0.261}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8675978, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.364, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8702059, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.404, "width_percent": 0.076}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.871813, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.48, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8754508, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.506, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.880598, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.772, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.883204, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.809, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.884815, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.886, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.888186, "duration": 0.00428, "duration_str": "4.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 81.91, "width_percent": 0.294}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.893776, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.203, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.8963778, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.237, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.898118, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.316, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.901793, "duration": 0.00357, "duration_str": "3.57ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.34, "width_percent": 0.245}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.907136, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.585, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.911071, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.619, "width_percent": 0.095}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.913008, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.714, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.916658, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 82.744, "width_percent": 0.258}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.921448, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.002, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.9240708, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.037, "width_percent": 0.075}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.92571, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.112, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.92927, "duration": 0.00375, "duration_str": "3.75ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.139, "width_percent": 0.257}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.934293, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.396, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.936959, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.431, "width_percent": 0.073}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.938535, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.504, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.942204, "duration": 0.00368, "duration_str": "3.68ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.532, "width_percent": 0.252}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.947058, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.784, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.949599, "duration": 0.00244, "duration_str": "2.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.82, "width_percent": 0.167}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.952766, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 83.987, "width_percent": 0.029}, {"sql": "update `ec_products` set `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.9559522, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.017, "width_percent": 0.032}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.95722, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.048, "width_percent": 0.023}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.9594529, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.072, "width_percent": 0.087}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.961382, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.159, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.965056, "duration": 0.00435, "duration_str": "4.35ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.186, "width_percent": 0.298}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.970661, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.485, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.97309, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.52, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.9746969, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.598, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.978502, "duration": 0.00445, "duration_str": "4.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.624, "width_percent": 0.305}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.984144, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.929, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.986483, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 84.963, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.988179, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.047, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.991948, "duration": 0.00411, "duration_str": "4.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.074, "width_percent": 0.282}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.997442, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.356, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848162.9999442, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.389, "width_percent": 0.079}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0016139, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.467, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.005259, "duration": 0.004, "duration_str": "4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.494, "width_percent": 0.274}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.010626, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.768, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0133872, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.801, "width_percent": 0.091}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0152562, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.892, "width_percent": 0.031}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.018893, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 85.923, "width_percent": 0.248}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.023628, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.171, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.026472, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.208, "width_percent": 0.119}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.029292, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.327, "width_percent": 0.035}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.033087, "duration": 0.00544, "duration_str": "5.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.362, "width_percent": 0.373}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.039642, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.735, "width_percent": 0.037}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.042244, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.772, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0446231, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.856, "width_percent": 0.033}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.048402, "duration": 0.0034500000000000004, "duration_str": "3.45ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 86.889, "width_percent": 0.237}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.05292, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.125, "width_percent": 0.03}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.055364, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.155, "width_percent": 0.089}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0571558, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.245, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.064318, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.273, "width_percent": 0.254}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.069154, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.527, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.071607, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.561, "width_percent": 0.079}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.073247, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.64, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.077081, "duration": 0.00417, "duration_str": "4.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.665, "width_percent": 0.286}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0823271, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.951, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.084835, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 87.985, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0865018, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.064, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.0904489, "duration": 0.00532, "duration_str": "5.32ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.093, "width_percent": 0.365}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.096884, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.458, "width_percent": 0.033}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.099416, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.491, "width_percent": 0.088}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.1012518, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.579, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.104962, "duration": 0.00383, "duration_str": "3.83ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.607, "width_percent": 0.263}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.111262, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.87, "width_percent": 0.037}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.113838, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 88.907, "width_percent": 0.1}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.115768, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.007, "width_percent": 0.025}, {"sql": "update `ec_products` set `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.118715, "duration": 0.0036, "duration_str": "3.6ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.032, "width_percent": 0.247}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.123172, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.279, "width_percent": 0.023}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.125325, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.302, "width_percent": 0.121}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.127733, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.423, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.131395, "duration": 0.00439, "duration_str": "4.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.449, "width_percent": 0.301}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.137656, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.75, "width_percent": 0.038}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.140271, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.787, "width_percent": 0.081}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.1419919, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.868, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.145781, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 89.896, "width_percent": 0.26}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.1506991, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.156, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.1534128, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.191, "width_percent": 0.1}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.155376, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.292, "width_percent": 0.027}, {"sql": "update `ec_products` set `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.1585338, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.319, "width_percent": 0.053}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.161181, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.372, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.163589, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.407, "width_percent": 0.093}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.1655118, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.501, "width_percent": 0.029}, {"sql": "update `ec_products` set `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.168588, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.53, "width_percent": 0.265}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.173456, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.794, "width_percent": 0.027}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.175699, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.822, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.177488, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.905, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.18117, "duration": 0.00352, "duration_str": "3.52ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 90.928, "width_percent": 0.241}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.185942, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.169, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.18946, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.209, "width_percent": 0.106}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.191761, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.315, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.195887, "duration": 0.0037, "duration_str": "3.7ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.346, "width_percent": 0.254}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.200773, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.6, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2035651, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.636, "width_percent": 0.082}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.205339, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.718, "width_percent": 0.027}, {"sql": "update `ec_products` set `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.208201, "duration": 0.00349, "duration_str": "3.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.744, "width_percent": 0.239}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2126641, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 91.984, "width_percent": 0.043}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.21711, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.027, "width_percent": 0.083}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.218847, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.11, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.22309, "duration": 0.0035299999999999997, "duration_str": "3.53ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.135, "width_percent": 0.242}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.228078, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.377, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.231556, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.418, "width_percent": 0.102}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2336729, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.52, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.237354, "duration": 0.004030000000000001, "duration_str": "4.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.549, "width_percent": 0.276}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2426, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.825, "width_percent": 0.04}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2457771, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.865, "width_percent": 0.104}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.247796, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.969, "width_percent": 0.026}, {"sql": "update `ec_products` set `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.250852, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 92.995, "width_percent": 0.032}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.252081, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.027, "width_percent": 0.023}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.254084, "duration": 0.00102, "duration_str": "1.02ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.05, "width_percent": 0.07}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.255571, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.12, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.259392, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.144, "width_percent": 0.267}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.264512, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.41, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.267081, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.445, "width_percent": 0.072}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.268603, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.517, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.271998, "duration": 0.00391, "duration_str": "3.91ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.541, "width_percent": 0.268}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.277111, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.81, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.279598, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.843, "width_percent": 0.076}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.281209, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.919, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2847428, "duration": 0.00376, "duration_str": "3.76ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 93.943, "width_percent": 0.258}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.289767, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.201, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.292559, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.237, "width_percent": 0.099}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.294735, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.336, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.2982352, "duration": 0.0038, "duration_str": "3.8ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.364, "width_percent": 0.261}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.30326, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.624, "width_percent": 0.037}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.3058681, "duration": 0.0012, "duration_str": "1.2ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.661, "width_percent": 0.082}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.307564, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.744, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.311605, "duration": 0.00398, "duration_str": "3.98ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 94.776, "width_percent": 0.273}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.316776, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.049, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.319223, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.085, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.320825, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.163, "width_percent": 0.024}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.324647, "duration": 0.00377, "duration_str": "3.77ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.187, "width_percent": 0.259}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.329655, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.446, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.332284, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.481, "width_percent": 0.077}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.333885, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.558, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.337249, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.581, "width_percent": 0.265}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.342333, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.846, "width_percent": 0.045}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.345208, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.891, "width_percent": 0.084}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.3469958, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 95.975, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.350625, "duration": 0.00365, "duration_str": "3.65ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.002, "width_percent": 0.25}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.355444, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.252, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.357977, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.288, "width_percent": 0.077}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.359585, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.365, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.363941, "duration": 0.00373, "duration_str": "3.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.397, "width_percent": 0.256}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.368732, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.653, "width_percent": 0.051}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.3714468, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.704, "width_percent": 0.077}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.3730469, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.781, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.376796, "duration": 0.004070000000000001, "duration_str": "4.07ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 96.807, "width_percent": 0.279}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.382123, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.086, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.384627, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.121, "width_percent": 0.082}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.386355, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.203, "width_percent": 0.027}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.390038, "duration": 0.00379, "duration_str": "3.79ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.23, "width_percent": 0.26}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.394947, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.49, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.397465, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.525, "width_percent": 0.083}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.399144, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.608, "width_percent": 0.029}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.402665, "duration": 0.0038599999999999997, "duration_str": "3.86ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.638, "width_percent": 0.265}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.407811, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.902, "width_percent": 0.036}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.4107141, "duration": 0.00133, "duration_str": "1.33ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 97.938, "width_percent": 0.091}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.412536, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.029, "width_percent": 0.026}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.4159632, "duration": 0.00381, "duration_str": "3.81ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.055, "width_percent": 0.261}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.421055, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.317, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.423675, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.352, "width_percent": 0.096}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.4256501, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.448, "width_percent": 0.032}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.4294991, "duration": 0.00396, "duration_str": "3.96ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.48, "width_percent": 0.272}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.434664, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.752, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.437169, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.785, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.438794, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.865, "width_percent": 0.023}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.442252, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 98.888, "width_percent": 0.278}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.447579, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.167, "width_percent": 0.035}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.4501548, "duration": 0.00114, "duration_str": "1.14ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.202, "width_percent": 0.078}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.451789, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.28, "width_percent": 0.025}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.455505, "duration": 0.00359, "duration_str": "3.59ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.304, "width_percent": 0.246}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.460318, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.551, "width_percent": 0.034}, {"sql": "select * from `ec_products` where `name` = ? and (`ec_products`.`is_variation` = ? or `ec_products`.`store_id` is null or not exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id`) or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `status` = ?)) limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.462873, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.584, "width_percent": 0.08}, {"sql": "select * from `mp_stores` where `name` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.464567, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.665, "width_percent": 0.028}, {"sql": "update `ec_products` set `store_id` = ?, `main_type` = ?, `ec_products`.`updated_at` = ? where `id` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.468185, "duration": 0.0039900000000000005, "duration_str": "3.99ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.693, "width_percent": 0.274}, {"sql": "select exists(select * from `ec_product_variations` where `ec_product_variations`.`configurable_product_id` = ? and `ec_product_variations`.`configurable_product_id` is not null) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": 1749848163.473364, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "muhrak", "explain": null, "start_percent": 99.966, "width_percent": 0.034}, {"sql": "... 41792 additional queries are executed but now shown because of Debugbar query limits. Limits can be raised in the config (debugbar.options.db.soft/hard_limit)", "type": "info"}]}, "models": {"data": {"Botble\\Ecommerce\\Models\\Product": {"value": 10510, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "Botble\\Marketplace\\Models\\Store": {"value": 10039, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 20550, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/pro/update-csv", "action_name": "products.update.csv", "controller_action": "App\\Http\\Controllers\\TempController@importProductsFromCsv", "uri": "GET pro/update-csv", "controller": "App\\Http\\Controllers\\TempController@importProductsFromCsv<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=11\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fapp%2FHttp%2FControllers%2FTempController.php&line=11\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/TempController.php:11-46</a>", "middleware": "web", "duration": "245.6s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1750823081 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1750823081\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-743638065 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-743638065\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1947970690 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im80ajVzcHdOWHdBWnpmelo4dTJibHc9PSIsInZhbHVlIjoibHl3SXg5TUw0bmwzK0lRMzIrSmtvVC91b3RmNXR5Vjl2aTU3dUJYdU9BTTNwYlJ5SDlYdGE2UDZnYjBmcHRFa2JHb3RBcWpjaUZ4aDhJU1dGbWJYbmtYd2FXclRKbFg0c2FMbnhNTk9JUEpCQ29abHI5cm1UNDY3dlAvWmFSc2siLCJtYWMiOiJlYmM5NzYwY2ZhZTdhZWU4ZDM0OGQ0OGQxMWQ1MGU3ODZhNmE0ZmM4ZGI5N2UzNTI2ZWI0YjRiNDJkZTQ1OWE4IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6InBGeSsxYWI2R2ZmbEtwZXRRWVArZWc9PSIsInZhbHVlIjoiQVA5M3RZQ1puOEFQRSsrdTVNbFVMV0JGb0pDaHNoZGNoTFowWS9KSkZZREdndTcrNjdTcWlCM3ltZmZiaFEzY3lYbzMwVVRVMGZ5emZxSEd5QTczeCt4QUJoblNabFl2MExiQkVRUVFTK0V4SkFTZTJwa3FJeUtnbTVabUdyK3kiLCJtYWMiOiJjODgwZGE1ZjJiZTczNzk4YzM1Y2ZiNjU0YTJkYzIyN2ExZDk3NWI5OTAyYjI5ZWM3YThkNjRhOTQ3MzJiOGFjIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1947970690\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-931173580 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931173580\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-556402063 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 21:00:05 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-556402063\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1427227159 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://muhrak.gc/pro/update-csv</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1427227159\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/pro/update-csv", "action_name": "products.update.csv", "controller_action": "App\\Http\\Controllers\\TempController@importProductsFromCsv"}, "badge": null}}