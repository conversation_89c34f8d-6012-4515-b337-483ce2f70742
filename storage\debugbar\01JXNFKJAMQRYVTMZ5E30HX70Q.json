{"__meta": {"id": "01JXNFKJAMQRYVTMZ5E30HX70Q", "datetime": "2025-06-13 20:27:24", "utime": **********.373477, "method": "GET", "uri": "/admin/customers/edit/412", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749846435.502268, "end": **********.373492, "duration": 8.87122392654419, "duration_str": "8.87s", "measures": [{"label": "Booting", "start": 1749846435.502268, "relative_start": 0, "end": **********.218822, "relative_end": **********.218822, "duration": 0.****************, "duration_str": "717ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.218833, "relative_start": 0.****************, "end": **********.373494, "relative_end": 1.9073486328125e-06, "duration": 8.***************, "duration_str": "8.15s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.235517, "relative_start": 0.****************, "end": **********.24444, "relative_end": **********.24444, "duration": 0.008923053741455078, "duration_str": "8.92ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: plugins/ecommerce::customers.addresses.addresses", "start": **********.382617, "relative_start": 0.****************, "end": **********.382617, "relative_end": **********.382617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.183625, "relative_start": 2.***************, "end": **********.183625, "relative_end": **********.183625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.184256, "relative_start": 2.681988000869751, "end": **********.184256, "relative_end": **********.184256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.184653, "relative_start": 2.682384967803955, "end": **********.184653, "relative_end": **********.184653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.185169, "relative_start": 2.682900905609131, "end": **********.185169, "relative_end": **********.185169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.185831, "relative_start": 2.683562994003296, "end": **********.185831, "relative_end": **********.185831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": **********.186301, "relative_start": 2.684032917022705, "end": **********.186301, "relative_end": **********.186301, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.index", "start": **********.186719, "relative_start": 2.68445086479187, "end": **********.186719, "relative_end": **********.186719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": **********.187398, "relative_start": 2.6851298809051514, "end": **********.187398, "relative_end": **********.187398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.row", "start": **********.187901, "relative_start": 2.6856329441070557, "end": **********.187901, "relative_end": **********.187901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.index", "start": **********.188428, "relative_start": 2.68615984916687, "end": **********.188428, "relative_end": **********.188428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.index", "start": **********.188954, "relative_start": 2.6866860389709473, "end": **********.188954, "relative_end": **********.188954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::customers.addresses.address-actions", "start": **********.189868, "relative_start": 2.6875998973846436, "end": **********.189868, "relative_end": **********.189868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.335471, "relative_start": 2.833202838897705, "end": **********.335471, "relative_end": **********.335471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d9cf6f2adf46159095e24cd6ce9ab42b", "start": **********.338891, "relative_start": 2.836622953414917, "end": **********.338891, "relative_end": **********.338891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::customers.wishlist", "start": **********.339467, "relative_start": 2.8371989727020264, "end": **********.339467, "relative_end": **********.339467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846439.442826, "relative_start": 3.9405579566955566, "end": 1749846439.442826, "relative_end": 1749846439.442826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846439.443139, "relative_start": 3.940871000289917, "end": 1749846439.443139, "relative_end": 1749846439.443139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846439.444258, "relative_start": 3.9419898986816406, "end": 1749846439.444258, "relative_end": 1749846439.444258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.index", "start": 1749846439.44459, "relative_start": 3.942322015762329, "end": 1749846439.44459, "relative_end": 1749846439.44459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": 1749846439.445913, "relative_start": 3.9436450004577637, "end": 1749846439.445913, "relative_end": 1749846439.445913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.row", "start": 1749846439.446242, "relative_start": 3.943974018096924, "end": 1749846439.446242, "relative_end": 1749846439.446242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.index", "start": 1749846439.446563, "relative_start": 3.9442949295043945, "end": 1749846439.446563, "relative_end": 1749846439.446563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.index", "start": 1749846439.446868, "relative_start": 3.9445998668670654, "end": 1749846439.446868, "relative_end": 1749846439.446868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::customers.payments.payments", "start": 1749846439.484545, "relative_start": 3.9822769165039062, "end": 1749846439.484545, "relative_end": 1749846439.484545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.626911, "relative_start": 6.124642848968506, "end": 1749846441.626911, "relative_end": 1749846441.626911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.627281, "relative_start": 6.1250128746032715, "end": 1749846441.627281, "relative_end": 1749846441.627281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.627509, "relative_start": 6.125241041183472, "end": 1749846441.627509, "relative_end": 1749846441.627509, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.627723, "relative_start": 6.125454902648926, "end": 1749846441.627723, "relative_end": 1749846441.627723, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.627932, "relative_start": 6.125663995742798, "end": 1749846441.627932, "relative_end": 1749846441.627932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.62814, "relative_start": 6.125871896743774, "end": 1749846441.62814, "relative_end": 1749846441.62814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.cell", "start": 1749846441.628439, "relative_start": 6.126170873641968, "end": 1749846441.628439, "relative_end": 1749846441.628439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.header.index", "start": 1749846441.628629, "relative_start": 6.126360893249512, "end": 1749846441.628629, "relative_end": 1749846441.628629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.cell", "start": 1749846441.628904, "relative_start": 6.126636028289795, "end": 1749846441.628904, "relative_end": 1749846441.628904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.row", "start": 1749846441.629126, "relative_start": 6.1268579959869385, "end": 1749846441.629126, "relative_end": 1749846441.629126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.body.index", "start": 1749846441.62934, "relative_start": 6.127071857452393, "end": 1749846441.62934, "relative_end": 1749846441.62934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::table.index", "start": 1749846441.629553, "relative_start": 6.127285003662109, "end": 1749846441.629553, "relative_end": 1749846441.629553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": 1749846441.630459, "relative_start": 6.128190994262695, "end": 1749846441.630459, "relative_end": 1749846441.630459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": 1749846441.631605, "relative_start": 6.129336833953857, "end": 1749846441.631605, "relative_end": 1749846441.631605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": 1749846441.632494, "relative_start": 6.130225896835327, "end": 1749846441.632494, "relative_end": 1749846441.632494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": 1749846441.633435, "relative_start": 6.131166934967041, "end": 1749846441.633435, "relative_end": 1749846441.633435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": 1749846441.633752, "relative_start": 6.131484031677246, "end": 1749846441.633752, "relative_end": 1749846441.633752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::customers.control", "start": 1749846441.637019, "relative_start": 6.134750843048096, "end": 1749846441.637019, "relative_end": 1749846441.637019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": 1749846441.816531, "relative_start": 6.314262866973877, "end": 1749846441.816531, "relative_end": 1749846441.816531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::customers.form", "start": 1749846441.824012, "relative_start": 6.321743965148926, "end": 1749846441.824012, "relative_end": 1749846441.824012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-content-only", "start": **********.140064, "relative_start": 7.637795925140381, "end": **********.140064, "relative_end": **********.140064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": **********.141123, "relative_start": 7.63885498046875, "end": **********.141123, "relative_end": **********.141123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.142175, "relative_start": 7.639906883239746, "end": **********.142175, "relative_end": **********.142175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.142931, "relative_start": 7.640662908554077, "end": **********.142931, "relative_end": **********.142931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.143707, "relative_start": 7.641438961029053, "end": **********.143707, "relative_end": **********.143707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.144155, "relative_start": 7.641886949539185, "end": **********.144155, "relative_end": **********.144155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.145574, "relative_start": 7.643306016921997, "end": **********.145574, "relative_end": **********.145574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.146113, "relative_start": 7.643844842910767, "end": **********.146113, "relative_end": **********.146113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.146705, "relative_start": 7.644436836242676, "end": **********.146705, "relative_end": **********.146705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.147258, "relative_start": 7.644989967346191, "end": **********.147258, "relative_end": **********.147258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.147717, "relative_start": 7.645448923110962, "end": **********.147717, "relative_end": **********.147717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.148146, "relative_start": 7.645877838134766, "end": **********.148146, "relative_end": **********.148146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.148385, "relative_start": 7.6461169719696045, "end": **********.148385, "relative_end": **********.148385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.148646, "relative_start": 7.646378040313721, "end": **********.148646, "relative_end": **********.148646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.148978, "relative_start": 7.64670991897583, "end": **********.148978, "relative_end": **********.148978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.149333, "relative_start": 7.647064924240112, "end": **********.149333, "relative_end": **********.149333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.149731, "relative_start": 7.647462844848633, "end": **********.149731, "relative_end": **********.149731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.150188, "relative_start": 7.6479198932647705, "end": **********.150188, "relative_end": **********.150188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.150697, "relative_start": 7.648428916931152, "end": **********.150697, "relative_end": **********.150697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.150934, "relative_start": 7.648665904998779, "end": **********.150934, "relative_end": **********.150934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.151194, "relative_start": 7.648926019668579, "end": **********.151194, "relative_end": **********.151194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.email", "start": **********.151622, "relative_start": 7.649353981018066, "end": **********.151622, "relative_end": **********.151622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.152353, "relative_start": 7.650084972381592, "end": **********.152353, "relative_end": **********.152353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.152692, "relative_start": 7.650424003601074, "end": **********.152692, "relative_end": **********.152692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.153051, "relative_start": 7.650782823562622, "end": **********.153051, "relative_end": **********.153051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.153498, "relative_start": 7.6512298583984375, "end": **********.153498, "relative_end": **********.153498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.153734, "relative_start": 7.651465892791748, "end": **********.153734, "relative_end": **********.153734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.153994, "relative_start": 7.651726007461548, "end": **********.153994, "relative_end": **********.153994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.custom-select", "start": **********.154446, "relative_start": 7.652177810668945, "end": **********.154446, "relative_end": **********.154446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.154937, "relative_start": 7.6526689529418945, "end": **********.154937, "relative_end": **********.154937, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.155453, "relative_start": 7.65318489074707, "end": **********.155453, "relative_end": **********.155453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.custom-select", "start": **********.155942, "relative_start": 7.653673887252808, "end": **********.155942, "relative_end": **********.155942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.157351, "relative_start": 7.655082941055298, "end": **********.157351, "relative_end": **********.157351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.157812, "relative_start": 7.65554404258728, "end": **********.157812, "relative_end": **********.157812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.158031, "relative_start": 7.6557629108428955, "end": **********.158031, "relative_end": **********.158031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.158285, "relative_start": 7.656016826629639, "end": **********.158285, "relative_end": **********.158285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.158647, "relative_start": 7.656378984451294, "end": **********.158647, "relative_end": **********.158647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.159012, "relative_start": 7.656744003295898, "end": **********.159012, "relative_end": **********.159012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.159361, "relative_start": 7.657092809677124, "end": **********.159361, "relative_end": **********.159361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.159728, "relative_start": 7.65745997428894, "end": **********.159728, "relative_end": **********.159728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.160152, "relative_start": 7.657883882522583, "end": **********.160152, "relative_end": **********.160152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.160414, "relative_start": 7.658145904541016, "end": **********.160414, "relative_end": **********.160414, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.160673, "relative_start": 7.65840482711792, "end": **********.160673, "relative_end": **********.160673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.160997, "relative_start": 7.658728837966919, "end": **********.160997, "relative_end": **********.160997, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.161355, "relative_start": 7.6590869426727295, "end": **********.161355, "relative_end": **********.161355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.161693, "relative_start": 7.6594250202178955, "end": **********.161693, "relative_end": **********.161693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.162059, "relative_start": 7.659790992736816, "end": **********.162059, "relative_end": **********.162059, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.162492, "relative_start": 7.660223960876465, "end": **********.162492, "relative_end": **********.162492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.16273, "relative_start": 7.660461902618408, "end": **********.16273, "relative_end": **********.16273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.162994, "relative_start": 7.660725831985474, "end": **********.162994, "relative_end": **********.162994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.163317, "relative_start": 7.661048889160156, "end": **********.163317, "relative_end": **********.163317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.163673, "relative_start": 7.661404848098755, "end": **********.163673, "relative_end": **********.163673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.164012, "relative_start": 7.661743879318237, "end": **********.164012, "relative_end": **********.164012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.164376, "relative_start": 7.662107944488525, "end": **********.164376, "relative_end": **********.164376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.164806, "relative_start": 7.6625378131866455, "end": **********.164806, "relative_end": **********.164806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.165035, "relative_start": 7.662766933441162, "end": **********.165035, "relative_end": **********.165035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.165423, "relative_start": 7.66315484046936, "end": **********.165423, "relative_end": **********.165423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.checkbox", "start": **********.16596, "relative_start": 7.663691997528076, "end": **********.16596, "relative_end": **********.16596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.353984, "relative_start": 7.851716041564941, "end": **********.353984, "relative_end": **********.353984, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.355029, "relative_start": 7.8527610301971436, "end": **********.355029, "relative_end": **********.355029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.355654, "relative_start": 7.853385925292969, "end": **********.355654, "relative_end": **********.355654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.355894, "relative_start": 7.853626012802124, "end": **********.355894, "relative_end": **********.355894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.356166, "relative_start": 7.8538978099823, "end": **********.356166, "relative_end": **********.356166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.356531, "relative_start": 7.854262828826904, "end": **********.356531, "relative_end": **********.356531, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.35687, "relative_start": 7.854601860046387, "end": **********.35687, "relative_end": **********.35687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.357449, "relative_start": 7.855180978775024, "end": **********.357449, "relative_end": **********.357449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.357685, "relative_start": 7.855417013168335, "end": **********.357685, "relative_end": **********.357685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.357946, "relative_start": 7.855677843093872, "end": **********.357946, "relative_end": **********.357946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.358247, "relative_start": 7.855978965759277, "end": **********.358247, "relative_end": **********.358247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.358573, "relative_start": 7.856304883956909, "end": **********.358573, "relative_end": **********.358573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.358992, "relative_start": 7.85672402381897, "end": **********.358992, "relative_end": **********.358992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.359244, "relative_start": 7.85697603225708, "end": **********.359244, "relative_end": **********.359244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.359501, "relative_start": 7.8572328090667725, "end": **********.359501, "relative_end": **********.359501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": **********.360076, "relative_start": 7.857807874679565, "end": **********.360076, "relative_end": **********.360076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.403625, "relative_start": 7.901356935501099, "end": **********.403625, "relative_end": **********.403625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.422617, "relative_start": 7.920348882675171, "end": **********.422617, "relative_end": **********.422617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.423987, "relative_start": 7.9217188358306885, "end": **********.423987, "relative_end": **********.423987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.424619, "relative_start": 7.922350883483887, "end": **********.424619, "relative_end": **********.424619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.425131, "relative_start": 7.922863006591797, "end": **********.425131, "relative_end": **********.425131, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.425826, "relative_start": 7.923557996749878, "end": **********.425826, "relative_end": **********.425826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::loading", "start": **********.426191, "relative_start": 7.923923015594482, "end": **********.426191, "relative_end": **********.426191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.426526, "relative_start": 7.92425799369812, "end": **********.426526, "relative_end": **********.426526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.427091, "relative_start": 7.924822807312012, "end": **********.427091, "relative_end": **********.427091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.427594, "relative_start": 7.925325870513916, "end": **********.427594, "relative_end": **********.427594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.428234, "relative_start": 7.925966024398804, "end": **********.428234, "relative_end": **********.428234, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.428545, "relative_start": 7.926276922225952, "end": **********.428545, "relative_end": **********.428545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.4291, "relative_start": 7.926831960678101, "end": **********.4291, "relative_end": **********.4291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.429605, "relative_start": 7.927336931228638, "end": **********.429605, "relative_end": **********.429605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.430246, "relative_start": 7.927978038787842, "end": **********.430246, "relative_end": **********.430246, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/marketplace::customers.control-modal", "start": **********.431645, "relative_start": 7.929376840591431, "end": **********.431645, "relative_end": **********.431645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.728389, "relative_start": 8.226120948791504, "end": **********.728389, "relative_end": **********.728389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.72946, "relative_start": 8.227191925048828, "end": **********.72946, "relative_end": **********.72946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.730075, "relative_start": 8.227806806564331, "end": **********.730075, "relative_end": **********.730075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.730727, "relative_start": 8.228458881378174, "end": **********.730727, "relative_end": **********.730727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.731322, "relative_start": 8.229053974151611, "end": **********.731322, "relative_end": **********.731322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b61dc00cf92742620aec4bf35aea701a", "start": **********.732263, "relative_start": 8.229995012283325, "end": **********.732263, "relative_end": **********.732263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.732539, "relative_start": 8.230270862579346, "end": **********.732539, "relative_end": **********.732539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.734047, "relative_start": 8.231778860092163, "end": **********.734047, "relative_end": **********.734047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::bulk-changes", "start": **********.749709, "relative_start": 8.247440814971924, "end": **********.749709, "relative_end": **********.749709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.750396, "relative_start": 8.248127937316895, "end": **********.750396, "relative_end": **********.750396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3952f2f8079cae92614235bd0a03d56", "start": **********.75153, "relative_start": 8.249261856079102, "end": **********.75153, "relative_end": **********.75153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.751826, "relative_start": 8.249557971954346, "end": **********.751826, "relative_end": **********.751826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.752317, "relative_start": 8.250048875808716, "end": **********.752317, "relative_end": **********.752317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.754102, "relative_start": 8.25183391571045, "end": **********.754102, "relative_end": **********.754102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.755007, "relative_start": 8.252738952636719, "end": **********.755007, "relative_end": **********.755007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::badge", "start": **********.755461, "relative_start": 8.253192901611328, "end": **********.755461, "relative_end": **********.755461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::simple-table", "start": **********.763557, "relative_start": 8.26128888130188, "end": **********.763557, "relative_end": **********.763557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.764662, "relative_start": 8.262393951416016, "end": **********.764662, "relative_end": **********.764662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.765029, "relative_start": 8.262760877609253, "end": **********.765029, "relative_end": **********.765029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: datatables::script", "start": **********.766998, "relative_start": 8.264729976654053, "end": **********.766998, "relative_end": **********.766998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.767375, "relative_start": 8.265106916427612, "end": **********.767375, "relative_end": **********.767375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.form-tabs", "start": **********.767785, "relative_start": 8.265516996383667, "end": **********.767785, "relative_end": **********.767785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::tab.item", "start": **********.771364, "relative_start": 8.26909589767456, "end": **********.771364, "relative_end": **********.771364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::tab.index", "start": **********.772326, "relative_start": 8.270057916641235, "end": **********.772326, "relative_end": **********.772326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.772926, "relative_start": 8.270658016204834, "end": **********.772926, "relative_end": **********.772926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.form-open-wrapper", "start": **********.773516, "relative_start": 8.271247863769531, "end": **********.773516, "relative_end": **********.773516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.773936, "relative_start": 8.271667957305908, "end": **********.773936, "relative_end": **********.773936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.77433, "relative_start": 8.272061824798584, "end": **********.77433, "relative_end": **********.77433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.774686, "relative_start": 8.272418022155762, "end": **********.774686, "relative_end": **********.774686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.775047, "relative_start": 8.272778987884521, "end": **********.775047, "relative_end": **********.775047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.77548, "relative_start": 8.27321195602417, "end": **********.77548, "relative_end": **********.77548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.775743, "relative_start": 8.273474931716919, "end": **********.775743, "relative_end": **********.775743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.776015, "relative_start": 8.273746967315674, "end": **********.776015, "relative_end": **********.776015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.776639, "relative_start": 8.274370908737183, "end": **********.776639, "relative_end": **********.776639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.77701, "relative_start": 8.274741888046265, "end": **********.77701, "relative_end": **********.77701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.777345, "relative_start": 8.275076866149902, "end": **********.777345, "relative_end": **********.777345, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.777694, "relative_start": 8.275425910949707, "end": **********.777694, "relative_end": **********.777694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.778122, "relative_start": 8.275853872299194, "end": **********.778122, "relative_end": **********.778122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.778335, "relative_start": 8.276067018508911, "end": **********.778335, "relative_end": **********.778335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.778593, "relative_start": 8.276324987411499, "end": **********.778593, "relative_end": **********.778593, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.779032, "relative_start": 8.276763916015625, "end": **********.779032, "relative_end": **********.779032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.779552, "relative_start": 8.277283906936646, "end": **********.779552, "relative_end": **********.779552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.780071, "relative_start": 8.27780294418335, "end": **********.780071, "relative_end": **********.780071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.780487, "relative_start": 8.278218984603882, "end": **********.780487, "relative_end": **********.780487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.78091, "relative_start": 8.278641939163208, "end": **********.78091, "relative_end": **********.78091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.781125, "relative_start": 8.278856992721558, "end": **********.781125, "relative_end": **********.781125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.781362, "relative_start": 8.279093980789185, "end": **********.781362, "relative_end": **********.781362, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.text", "start": **********.781675, "relative_start": 8.279407024383545, "end": **********.781675, "relative_end": **********.781675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.782028, "relative_start": 8.279759883880615, "end": **********.782028, "relative_end": **********.782028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.782344, "relative_start": 8.280076026916504, "end": **********.782344, "relative_end": **********.782344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.782679, "relative_start": 8.280411005020142, "end": **********.782679, "relative_end": **********.782679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.783107, "relative_start": 8.280838966369629, "end": **********.783107, "relative_end": **********.783107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.783321, "relative_start": 8.281052827835083, "end": **********.783321, "relative_end": **********.783321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.783582, "relative_start": 8.2813138961792, "end": **********.783582, "relative_end": **********.783582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.date-picker", "start": **********.784012, "relative_start": 8.281744003295898, "end": **********.784012, "relative_end": **********.784012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.784761, "relative_start": 8.282492876052856, "end": **********.784761, "relative_end": **********.784761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.785081, "relative_start": 8.28281283378601, "end": **********.785081, "relative_end": **********.785081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.date-picker", "start": **********.785557, "relative_start": 8.283288955688477, "end": **********.785557, "relative_end": **********.785557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.786315, "relative_start": 8.28404688835144, "end": **********.786315, "relative_end": **********.786315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::71e661e6e74c892ee0d38d1089b1cb44", "start": **********.787468, "relative_start": 8.285199880599976, "end": **********.787468, "relative_end": **********.787468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.789434, "relative_start": 8.287165880203247, "end": **********.789434, "relative_end": **********.789434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b9129d3adbe29512c27ed8c014c531f8", "start": **********.790832, "relative_start": 8.288563966751099, "end": **********.790832, "relative_end": **********.790832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.808002, "relative_start": 8.305733919143677, "end": **********.808002, "relative_end": **********.808002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.80854, "relative_start": 8.306272029876709, "end": **********.80854, "relative_end": **********.80854, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.808768, "relative_start": 8.30649995803833, "end": **********.808768, "relative_end": **********.808768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.809052, "relative_start": 8.30678391456604, "end": **********.809052, "relative_end": **********.809052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.on-off", "start": **********.809439, "relative_start": 8.307170867919922, "end": **********.809439, "relative_end": **********.809439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.on-off", "start": **********.809812, "relative_start": 8.307543992996216, "end": **********.809812, "relative_end": **********.809812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.toggle", "start": **********.810164, "relative_start": 8.30789589881897, "end": **********.810164, "relative_end": **********.810164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.810533, "relative_start": 8.308264970779419, "end": **********.810533, "relative_end": **********.810533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.810955, "relative_start": 8.308686971664429, "end": **********.810955, "relative_end": **********.810955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.811188, "relative_start": 8.308919906616211, "end": **********.811188, "relative_end": **********.811188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.811443, "relative_start": 8.30917501449585, "end": **********.811443, "relative_end": **********.811443, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": **********.811792, "relative_start": 8.309523820877075, "end": **********.811792, "relative_end": **********.811792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.819741, "relative_start": 8.3174729347229, "end": **********.819741, "relative_end": **********.819741, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.820194, "relative_start": 8.317925930023193, "end": **********.820194, "relative_end": **********.820194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.820896, "relative_start": 8.318627834320068, "end": **********.820896, "relative_end": **********.820896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravel-form-builder::text", "start": **********.821293, "relative_start": 8.319025039672852, "end": **********.821293, "relative_end": **********.821293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.821639, "relative_start": 8.319370985031128, "end": **********.821639, "relative_end": **********.821639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.822065, "relative_start": 8.319797039031982, "end": **********.822065, "relative_end": **********.822065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.822724, "relative_start": 8.32045602798462, "end": **********.822724, "relative_end": **********.822724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.textarea", "start": **********.823208, "relative_start": 8.320940017700195, "end": **********.823208, "relative_end": **********.823208, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.823852, "relative_start": 8.321583986282349, "end": **********.823852, "relative_end": **********.823852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.824194, "relative_start": 8.32192587852478, "end": **********.824194, "relative_end": **********.824194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.824574, "relative_start": 8.322305917739868, "end": **********.824574, "relative_end": **********.824574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.825015, "relative_start": 8.322746992111206, "end": **********.825015, "relative_end": **********.825015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.825252, "relative_start": 8.322983980178833, "end": **********.825252, "relative_end": **********.825252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.82551, "relative_start": 8.323241949081421, "end": **********.82551, "relative_end": **********.82551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::tab.pane", "start": **********.825875, "relative_start": 8.323606967926025, "end": **********.825875, "relative_end": **********.825875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::tab.content", "start": **********.826987, "relative_start": 8.324718952178955, "end": **********.826987, "relative_end": **********.826987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.827504, "relative_start": 8.325235843658447, "end": **********.827504, "relative_end": **********.827504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.827797, "relative_start": 8.325528860092163, "end": **********.827797, "relative_end": **********.827797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.828226, "relative_start": 8.325958013534546, "end": **********.828226, "relative_end": **********.828226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.829396, "relative_start": 8.327127933502197, "end": **********.829396, "relative_end": **********.829396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": **********.82988, "relative_start": 8.327611923217773, "end": **********.82988, "relative_end": **********.82988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.830195, "relative_start": 8.327926874160767, "end": **********.830195, "relative_end": **********.830195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.830406, "relative_start": 8.328137874603271, "end": **********.830406, "relative_end": **********.830406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.830703, "relative_start": 8.328434944152832, "end": **********.830703, "relative_end": **********.830703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.831375, "relative_start": 8.32910680770874, "end": **********.831375, "relative_end": **********.831375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.831665, "relative_start": 8.329396963119507, "end": **********.831665, "relative_end": **********.831665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.831877, "relative_start": 8.329608917236328, "end": **********.831877, "relative_end": **********.831877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.meta-box", "start": **********.832174, "relative_start": 8.329905986785889, "end": **********.832174, "relative_end": **********.832174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.832848, "relative_start": 8.330579996109009, "end": **********.832848, "relative_end": **********.832848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.833137, "relative_start": 8.33086895942688, "end": **********.833137, "relative_end": **********.833137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.833347, "relative_start": 8.331079006195068, "end": **********.833347, "relative_end": **********.833347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.836787, "relative_start": 8.334518909454346, "end": **********.836787, "relative_end": **********.836787, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-actions", "start": **********.837318, "relative_start": 8.335049867630005, "end": **********.837318, "relative_end": **********.837318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.838215, "relative_start": 8.335947036743164, "end": **********.838215, "relative_end": **********.838215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.838595, "relative_start": 8.336326837539673, "end": **********.838595, "relative_end": **********.838595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.83894, "relative_start": 8.336671829223633, "end": **********.83894, "relative_end": **********.83894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.8396, "relative_start": 8.337332010269165, "end": **********.8396, "relative_end": **********.8396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.840963, "relative_start": 8.33869481086731, "end": **********.840963, "relative_end": **********.840963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.841277, "relative_start": 8.339008808135986, "end": **********.841277, "relative_end": **********.841277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.842408, "relative_start": 8.***************, "end": **********.842408, "relative_end": **********.842408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.842968, "relative_start": 8.***************, "end": **********.842968, "relative_end": **********.842968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.843214, "relative_start": 8.***************, "end": **********.843214, "relative_end": **********.843214, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.breadcrumbs", "start": **********.846317, "relative_start": 8.***************, "end": **********.846317, "relative_end": **********.846317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.form-buttons", "start": **********.84666, "relative_start": 8.***************, "end": **********.84666, "relative_end": **********.84666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.847181, "relative_start": 8.344913005828857, "end": **********.847181, "relative_end": **********.847181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d25e365b684c7fa9ad5ec13cfb768fc1", "start": **********.847758, "relative_start": 8.345489978790283, "end": **********.847758, "relative_end": **********.847758, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.847992, "relative_start": 8.345723867416382, "end": **********.847992, "relative_end": **********.847992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5db12cde11beb11dd9ef0499874ec197", "start": **********.84855, "relative_start": 8.346282005310059, "end": **********.84855, "relative_end": **********.84855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.85022, "relative_start": 8.347951889038086, "end": **********.85022, "relative_end": **********.85022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.85049, "relative_start": 8.348222017288208, "end": **********.85049, "relative_end": **********.85049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.85089, "relative_start": 8.***************, "end": **********.85089, "relative_end": **********.85089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.851269, "relative_start": 8.***************, "end": **********.851269, "relative_end": **********.851269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.851704, "relative_start": 8.***************, "end": **********.851704, "relative_end": **********.851704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.html", "start": **********.852023, "relative_start": 8.***************, "end": **********.852023, "relative_end": **********.852023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.852373, "relative_start": 8.350104808807373, "end": **********.852373, "relative_end": **********.852373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.852818, "relative_start": 8.350549936294556, "end": **********.852818, "relative_end": **********.852818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.853074, "relative_start": 8.35080599784851, "end": **********.853074, "relative_end": **********.853074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.853354, "relative_start": 8.351085901260376, "end": **********.853354, "relative_end": **********.853354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.853653, "relative_start": 8.35138487815857, "end": **********.853653, "relative_end": **********.853653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.853909, "relative_start": 8.351640939712524, "end": **********.853909, "relative_end": **********.853909, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.label", "start": **********.854288, "relative_start": 8.352020025253296, "end": **********.854288, "relative_end": **********.854288, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.854658, "relative_start": 8.352389812469482, "end": **********.854658, "relative_end": **********.854658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.855024, "relative_start": 8.352756023406982, "end": **********.855024, "relative_end": **********.855024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.855519, "relative_start": 8.353250980377197, "end": **********.855519, "relative_end": **********.855519, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.fields.media-image", "start": **********.856, "relative_start": 8.353731870651245, "end": **********.856, "relative_end": **********.856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.image", "start": **********.85675, "relative_start": 8.354481935501099, "end": **********.85675, "relative_end": **********.85675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.image", "start": **********.857538, "relative_start": 8.35526990890503, "end": **********.857538, "relative_end": **********.857538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::image", "start": **********.870501, "relative_start": 8.36823296546936, "end": **********.870501, "relative_end": **********.870501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.871008, "relative_start": 8.36873984336853, "end": **********.871008, "relative_end": **********.871008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1c3e3c2b5f3306f2c083e21b9339fe63", "start": **********.872006, "relative_start": 8.36973786354065, "end": **********.872006, "relative_end": **********.872006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.field", "start": **********.873914, "relative_start": 8.3716459274292, "end": **********.873914, "relative_end": **********.873914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.help-block", "start": **********.874375, "relative_start": 8.372107028961182, "end": **********.874375, "relative_end": **********.874375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.partials.errors", "start": **********.87459, "relative_start": 8.372321844100952, "end": **********.87459, "relative_end": **********.87459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::forms.columns.column-span", "start": **********.874839, "relative_start": 8.***************, "end": **********.874839, "relative_end": **********.874839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.body.index", "start": **********.875093, "relative_start": 8.***************, "end": **********.875093, "relative_end": **********.875093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.875309, "relative_start": 8.***************, "end": **********.875309, "relative_end": **********.875309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.meta-box", "start": **********.876728, "relative_start": 8.***************, "end": **********.876728, "relative_end": **********.876728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/js-validation::bootstrap", "start": **********.882374, "relative_start": 8.380105972290039, "end": **********.882374, "relative_end": **********.882374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.master", "start": **********.883031, "relative_start": 8.380762815475464, "end": **********.883031, "relative_end": **********.883031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.before-content", "start": **********.883915, "relative_start": 8.381646871566772, "end": **********.883915, "relative_end": **********.883915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.header", "start": **********.884332, "relative_start": 8.382063865661621, "end": **********.884332, "relative_end": **********.884332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.885555, "relative_start": 8.383286952972412, "end": **********.885555, "relative_end": **********.885555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.885934, "relative_start": 8.383666038513184, "end": **********.885934, "relative_end": **********.885934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.navbar-input", "start": **********.886479, "relative_start": 8.38421082496643, "end": **********.886479, "relative_end": **********.886479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.887006, "relative_start": 8.384737968444824, "end": **********.887006, "relative_end": **********.887006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.887885, "relative_start": 8.385617017745972, "end": **********.887885, "relative_end": **********.887885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.888382, "relative_start": 8.38611388206482, "end": **********.888382, "relative_end": **********.888382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.888807, "relative_start": 8.386538982391357, "end": **********.888807, "relative_end": **********.888807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.889929, "relative_start": 8.38766098022461, "end": **********.889929, "relative_end": **********.889929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dc988bb05638c97d86c3bc8a9b727e31", "start": **********.890985, "relative_start": 8.38871693611145, "end": **********.890985, "relative_end": **********.890985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.theme-toggle", "start": **********.891358, "relative_start": 8.389089822769165, "end": **********.891358, "relative_end": **********.891358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ec711b4f47c4b42ebc81a7564c1d8d33", "start": **********.892552, "relative_start": 8.390283823013306, "end": **********.892552, "relative_end": **********.892552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.nav-item", "start": **********.893818, "relative_start": 8.391549825668335, "end": **********.893818, "relative_end": **********.893818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::306e03d3dc5634ee2e82192f553c6f9b", "start": **********.894695, "relative_start": 8.39242696762085, "end": **********.894695, "relative_end": **********.894695, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/contact::partials.notification", "start": **********.897152, "relative_start": 8.394883871078491, "end": **********.897152, "relative_end": **********.897152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.898409, "relative_start": 8.396140813827515, "end": **********.898409, "relative_end": **********.898409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.title", "start": **********.899204, "relative_start": 8.396935939788818, "end": **********.899204, "relative_end": **********.899204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.actions", "start": **********.899599, "relative_start": 8.39733099937439, "end": **********.899599, "relative_end": **********.899599, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.header.index", "start": **********.899836, "relative_start": 8.397567987442017, "end": **********.899836, "relative_end": **********.899836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::card.index", "start": **********.920989, "relative_start": 8.***************, "end": **********.920989, "relative_end": **********.920989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.user-menu", "start": **********.923797, "relative_start": 8.***************, "end": **********.923797, "relative_end": **********.923797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.928766, "relative_start": 8.**************, "end": **********.928766, "relative_end": **********.928766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.92987, "relative_start": 8.42760181427002, "end": **********.92987, "relative_end": **********.92987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.930205, "relative_start": 8.427937030792236, "end": **********.930205, "relative_end": **********.930205, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.931176, "relative_start": 8.428907871246338, "end": **********.931176, "relative_end": **********.931176, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.93147, "relative_start": 8.42920184135437, "end": **********.93147, "relative_end": **********.93147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.aside", "start": **********.932145, "relative_start": 8.429877042770386, "end": **********.932145, "relative_end": **********.932145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::133aa97c11fca0f84f02ebcb9fd067dc", "start": **********.932911, "relative_start": 8.43064284324646, "end": **********.932911, "relative_end": **********.932911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.logo", "start": **********.933112, "relative_start": 8.430843830108643, "end": **********.933112, "relative_end": **********.933112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.93437, "relative_start": 8.432101964950562, "end": **********.93437, "relative_end": **********.93437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2907df26a6102c24ab0c37391217b338", "start": **********.934871, "relative_start": 8.432602882385254, "end": **********.934871, "relative_end": **********.934871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.item", "start": **********.935149, "relative_start": 8.432880878448486, "end": **********.935149, "relative_end": **********.935149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::f38bffca7b8a1a50e97a6950ffd66c5c", "start": **********.935704, "relative_start": 8.433435916900635, "end": **********.935704, "relative_end": **********.935704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::dropdown.index", "start": **********.935958, "relative_start": 8.433689832687378, "end": **********.935958, "relative_end": **********.935958, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.sidebar", "start": **********.936558, "relative_start": 8.434289932250977, "end": **********.936558, "relative_end": **********.936558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav", "start": **********.936976, "relative_start": 8.434707880020142, "end": **********.936976, "relative_end": **********.936976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.95457, "relative_start": 8.452301979064941, "end": **********.95457, "relative_end": **********.95457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.955421, "relative_start": 8.453152894973755, "end": **********.955421, "relative_end": **********.955421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::af68cda57c5ca67f3b8a7729953880bc", "start": **********.957403, "relative_start": 8.455134868621826, "end": **********.957403, "relative_end": **********.957403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.958209, "relative_start": 8.455940961837769, "end": **********.958209, "relative_end": **********.958209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.95896, "relative_start": 8.456691980361938, "end": **********.95896, "relative_end": **********.95896, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c50817c59f218fea1b6c19be4db347dd", "start": **********.960422, "relative_start": 8.45815396308899, "end": **********.960422, "relative_end": **********.960422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.961154, "relative_start": 8.458885908126831, "end": **********.961154, "relative_end": **********.961154, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.961692, "relative_start": 8.459424018859863, "end": **********.961692, "relative_end": **********.961692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.962204, "relative_start": 8.459935903549194, "end": **********.962204, "relative_end": **********.962204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::42d71f4fe12493bd6829a606380e1435", "start": **********.963938, "relative_start": 8.461669921875, "end": **********.963938, "relative_end": **********.963938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.964465, "relative_start": 8.462196826934814, "end": **********.964465, "relative_end": **********.964465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1a65d1071503a2e05e73a706bfebac58", "start": **********.967135, "relative_start": 8.464866876602173, "end": **********.967135, "relative_end": **********.967135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.96768, "relative_start": 8.465411901473999, "end": **********.96768, "relative_end": **********.96768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.967979, "relative_start": 8.465710878372192, "end": **********.967979, "relative_end": **********.967979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.968374, "relative_start": 8.466105937957764, "end": **********.968374, "relative_end": **********.968374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7e33c8bf87ccc47b22832a4eb415ca9d", "start": **********.969573, "relative_start": 8.467304944992065, "end": **********.969573, "relative_end": **********.969573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.970078, "relative_start": 8.467809915542603, "end": **********.970078, "relative_end": **********.970078, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::452ccf81d5bbacec7ec062999540e293", "start": **********.971189, "relative_start": 8.468920946121216, "end": **********.971189, "relative_end": **********.971189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.971729, "relative_start": 8.46946096420288, "end": **********.971729, "relative_end": **********.971729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.97202, "relative_start": 8.469751834869385, "end": **********.97202, "relative_end": **********.97202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.972385, "relative_start": 8.47011685371399, "end": **********.972385, "relative_end": **********.972385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ee52ba22bc1262334c4146935fbed227", "start": **********.974587, "relative_start": 8.472318887710571, "end": **********.974587, "relative_end": **********.974587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.975155, "relative_start": 8.47288703918457, "end": **********.975155, "relative_end": **********.975155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7bd0601fe0dc4645b269458a9ed5f144", "start": **********.976849, "relative_start": 8.474581003189087, "end": **********.976849, "relative_end": **********.976849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.977353, "relative_start": 8.475085020065308, "end": **********.977353, "relative_end": **********.977353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5e1ed162565cf31bd543a8427caaef1e", "start": **********.978457, "relative_start": 8.476188898086548, "end": **********.978457, "relative_end": **********.978457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.978971, "relative_start": 8.47670292854309, "end": **********.978971, "relative_end": **********.978971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.979244, "relative_start": 8.476975917816162, "end": **********.979244, "relative_end": **********.979244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.979614, "relative_start": 8.477345943450928, "end": **********.979614, "relative_end": **********.979614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e3de92deb18074b63f585befac7d0965", "start": **********.981082, "relative_start": 8.478813886642456, "end": **********.981082, "relative_end": **********.981082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.981631, "relative_start": 8.479362964630127, "end": **********.981631, "relative_end": **********.981631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::69ab4d8cea5c292eefba375a85e9768a", "start": **********.983452, "relative_start": 8.481184005737305, "end": **********.983452, "relative_end": **********.983452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.983959, "relative_start": 8.481690883636475, "end": **********.983959, "relative_end": **********.983959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::64f291ea9b97f679266b5e47f5ae8464", "start": **********.985027, "relative_start": 8.48275899887085, "end": **********.985027, "relative_end": **********.985027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.985517, "relative_start": 8.483248949050903, "end": **********.985517, "relative_end": **********.985517, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.986618, "relative_start": 8.484349966049194, "end": **********.986618, "relative_end": **********.986618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.98711, "relative_start": 8.48484182357788, "end": **********.98711, "relative_end": **********.98711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5d3f310865558ef9371c1fb4b468d742", "start": **********.988219, "relative_start": 8.485950946807861, "end": **********.988219, "relative_end": **********.988219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.988894, "relative_start": 8.486625909805298, "end": **********.988894, "relative_end": **********.988894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59b84d05fdb5c77612cfb696a101e213", "start": **********.990798, "relative_start": 8.488529920578003, "end": **********.990798, "relative_end": **********.990798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.991329, "relative_start": 8.489060878753662, "end": **********.991329, "relative_end": **********.991329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2c23a08a8a248b19ed43094ec82cfb6b", "start": **********.99293, "relative_start": 8.490661859512329, "end": **********.99293, "relative_end": **********.99293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.993419, "relative_start": 8.491150856018066, "end": **********.993419, "relative_end": **********.993419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::59b84d05fdb5c77612cfb696a101e213", "start": **********.994449, "relative_start": 8.492180824279785, "end": **********.994449, "relative_end": **********.994449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.994924, "relative_start": 8.492655992507935, "end": **********.994924, "relative_end": **********.994924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1859ae2c66dc0538fd83aadc7f316844", "start": **********.99641, "relative_start": 8.494141817092896, "end": **********.99641, "relative_end": **********.99641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.996923, "relative_start": 8.494654893875122, "end": **********.996923, "relative_end": **********.996923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ccda1407f97c36756562e2c01f09a7ab", "start": **********.998125, "relative_start": 8.495857000350952, "end": **********.998125, "relative_end": **********.998125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.998686, "relative_start": 8.496417999267578, "end": **********.998686, "relative_end": **********.998686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::52309442cd989852b03aa65d96b55790", "start": **********.999851, "relative_start": 8.497582912445068, "end": **********.999851, "relative_end": **********.999851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.001932, "relative_start": 8.499663829803467, "end": **********.001932, "relative_end": **********.001932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2cd2bd3a1a36cafc00007578f96771de", "start": **********.003582, "relative_start": 8.501313924789429, "end": **********.003582, "relative_end": **********.003582, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.004095, "relative_start": 8.501827001571655, "end": **********.004095, "relative_end": **********.004095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::24c50807435b9d6d46a8d65cd016c8b0", "start": **********.005841, "relative_start": 8.503572940826416, "end": **********.005841, "relative_end": **********.005841, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.006421, "relative_start": 8.50415301322937, "end": **********.006421, "relative_end": **********.006421, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::13365b7e5a448d13150fdb4b3884b510", "start": **********.008185, "relative_start": 8.505916833877563, "end": **********.008185, "relative_end": **********.008185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.008811, "relative_start": 8.506542921066284, "end": **********.008811, "relative_end": **********.008811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.009499, "relative_start": 8.507230997085571, "end": **********.009499, "relative_end": **********.009499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2279ce135fc9e268c1dcef75eaca22a5", "start": **********.011647, "relative_start": 8.509378910064697, "end": **********.011647, "relative_end": **********.011647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.012196, "relative_start": 8.509927988052368, "end": **********.012196, "relative_end": **********.012196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.0125, "relative_start": 8.510231971740723, "end": **********.0125, "relative_end": **********.0125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.013119, "relative_start": 8.51085090637207, "end": **********.013119, "relative_end": **********.013119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.014318, "relative_start": 8.512049913406372, "end": **********.014318, "relative_end": **********.014318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.01483, "relative_start": 8.512562036514282, "end": **********.01483, "relative_end": **********.01483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.015488, "relative_start": 8.513219833374023, "end": **********.015488, "relative_end": **********.015488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.015955, "relative_start": 8.513686895370483, "end": **********.015955, "relative_end": **********.015955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.016597, "relative_start": 8.514328956604004, "end": **********.016597, "relative_end": **********.016597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.017091, "relative_start": 8.514822959899902, "end": **********.017091, "relative_end": **********.017091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.018275, "relative_start": 8.51600694656372, "end": **********.018275, "relative_end": **********.018275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.01876, "relative_start": 8.516491889953613, "end": **********.01876, "relative_end": **********.01876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.019057, "relative_start": 8.516788959503174, "end": **********.019057, "relative_end": **********.019057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.019435, "relative_start": 8.51716685295105, "end": **********.019435, "relative_end": **********.019435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.020076, "relative_start": 8.517807960510254, "end": **********.020076, "relative_end": **********.020076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.020542, "relative_start": 8.518273830413818, "end": **********.020542, "relative_end": **********.020542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.021642, "relative_start": 8.519373893737793, "end": **********.021642, "relative_end": **********.021642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.022259, "relative_start": 8.519990921020508, "end": **********.022259, "relative_end": **********.022259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.022551, "relative_start": 8.520282983779907, "end": **********.022551, "relative_end": **********.022551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.02299, "relative_start": 8.520721912384033, "end": **********.02299, "relative_end": **********.02299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.02407, "relative_start": 8.521801948547363, "end": **********.02407, "relative_end": **********.02407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.02465, "relative_start": 8.522382020950317, "end": **********.02465, "relative_end": **********.02465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.025976, "relative_start": 8.523707866668701, "end": **********.025976, "relative_end": **********.025976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.026468, "relative_start": 8.524199962615967, "end": **********.026468, "relative_end": **********.026468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.027083, "relative_start": 8.52481484413147, "end": **********.027083, "relative_end": **********.027083, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "start": **********.028516, "relative_start": 8.52624797821045, "end": **********.028516, "relative_end": **********.028516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.029157, "relative_start": 8.526888847351074, "end": **********.029157, "relative_end": **********.029157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.029797, "relative_start": 8.527529001235962, "end": **********.029797, "relative_end": **********.029797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::998e4178ecae37dacf7321232f455f64", "start": **********.031008, "relative_start": 8.528739929199219, "end": **********.031008, "relative_end": **********.031008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.031523, "relative_start": 8.529254913330078, "end": **********.031523, "relative_end": **********.031523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::06e7cfabd119917d6efbd595a344e37b", "start": **********.033111, "relative_start": 8.530843019485474, "end": **********.033111, "relative_end": **********.033111, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.033614, "relative_start": 8.531345844268799, "end": **********.033614, "relative_end": **********.033614, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.03512, "relative_start": 8.532851934432983, "end": **********.03512, "relative_end": **********.03512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.035627, "relative_start": 8.533358812332153, "end": **********.035627, "relative_end": **********.035627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9413e731e9bb6e320e018067130903e9", "start": **********.03671, "relative_start": 8.534441947937012, "end": **********.03671, "relative_end": **********.03671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.037195, "relative_start": 8.534926891326904, "end": **********.037195, "relative_end": **********.037195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.037803, "relative_start": 8.535534858703613, "end": **********.037803, "relative_end": **********.037803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ca20cd1247722214b06db9aa7c493b27", "start": **********.039881, "relative_start": 8.537612915039062, "end": **********.039881, "relative_end": **********.039881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.040523, "relative_start": 8.538254976272583, "end": **********.040523, "relative_end": **********.040523, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.040839, "relative_start": 8.538570880889893, "end": **********.040839, "relative_end": **********.040839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.041273, "relative_start": 8.539005041122437, "end": **********.041273, "relative_end": **********.041273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.042032, "relative_start": 8.539763927459717, "end": **********.042032, "relative_end": **********.042032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.042469, "relative_start": 8.54020094871521, "end": **********.042469, "relative_end": **********.042469, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.042828, "relative_start": 8.540560007095337, "end": **********.042828, "relative_end": **********.042828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.043289, "relative_start": 8.54102087020874, "end": **********.043289, "relative_end": **********.043289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.044061, "relative_start": 8.541792869567871, "end": **********.044061, "relative_end": **********.044061, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.044678, "relative_start": 8.542409896850586, "end": **********.044678, "relative_end": **********.044678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.045418, "relative_start": 8.543149948120117, "end": **********.045418, "relative_end": **********.045418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.046029, "relative_start": 8.543761014938354, "end": **********.046029, "relative_end": **********.046029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.046742, "relative_start": 8.544473886489868, "end": **********.046742, "relative_end": **********.046742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fd978891e1ac33723cbffddc6658659a", "start": **********.048323, "relative_start": 8.54605484008789, "end": **********.048323, "relative_end": **********.048323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.048922, "relative_start": 8.546653985977173, "end": **********.048922, "relative_end": **********.048922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.049762, "relative_start": 8.547493934631348, "end": **********.049762, "relative_end": **********.049762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.050274, "relative_start": 8.548005819320679, "end": **********.050274, "relative_end": **********.050274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::d890ecc3acbc4ef41a8ece9e81698457", "start": **********.051002, "relative_start": 8.548733949661255, "end": **********.051002, "relative_end": **********.051002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.05152, "relative_start": 8.549252033233643, "end": **********.05152, "relative_end": **********.05152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.052182, "relative_start": 8.549913883209229, "end": **********.052182, "relative_end": **********.052182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.052904, "relative_start": 8.550635814666748, "end": **********.052904, "relative_end": **********.052904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.navbar.badge-count", "start": **********.053375, "relative_start": 8.551106929779053, "end": **********.053375, "relative_end": **********.053375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::navbar.badge-count", "start": **********.053696, "relative_start": 8.551427841186523, "end": **********.053696, "relative_end": **********.053696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.054135, "relative_start": 8.551867008209229, "end": **********.054135, "relative_end": **********.054135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::cbce7a4c13e13a70eabb7759894a60fd", "start": **********.055518, "relative_start": 8.553249835968018, "end": **********.055518, "relative_end": **********.055518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.056252, "relative_start": 8.553983926773071, "end": **********.056252, "relative_end": **********.056252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "start": **********.057855, "relative_start": 8.555586814880371, "end": **********.057855, "relative_end": **********.057855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.058549, "relative_start": 8.556280851364136, "end": **********.058549, "relative_end": **********.058549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.059276, "relative_start": 8.557008028030396, "end": **********.059276, "relative_end": **********.059276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e81d33e262b34e339fefa952b8ffa5f1", "start": **********.060837, "relative_start": 8.558568954467773, "end": **********.060837, "relative_end": **********.060837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.061538, "relative_start": 8.559269905090332, "end": **********.061538, "relative_end": **********.061538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.062409, "relative_start": 8.56014084815979, "end": **********.062409, "relative_end": **********.062409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::351bdfbe842eff22e08f1df9d5f5beb1", "start": **********.064552, "relative_start": 8.562283992767334, "end": **********.064552, "relative_end": **********.064552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.065155, "relative_start": 8.562886953353882, "end": **********.065155, "relative_end": **********.065155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fda51db955ba828a198ee1c8d52b2003", "start": **********.066446, "relative_start": 8.564177989959717, "end": **********.066446, "relative_end": **********.066446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.067001, "relative_start": 8.564733028411865, "end": **********.067001, "relative_end": **********.067001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.068171, "relative_start": 8.565902948379517, "end": **********.068171, "relative_end": **********.068171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.068702, "relative_start": 8.566433906555176, "end": **********.068702, "relative_end": **********.068702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.069426, "relative_start": 8.567157983779907, "end": **********.069426, "relative_end": **********.069426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "start": **********.07088, "relative_start": 8.568611860275269, "end": **********.07088, "relative_end": **********.07088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.07139, "relative_start": 8.569121837615967, "end": **********.07139, "relative_end": **********.07139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.072073, "relative_start": 8.569804906845093, "end": **********.072073, "relative_end": **********.072073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.073602, "relative_start": 8.571333885192871, "end": **********.073602, "relative_end": **********.073602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.074575, "relative_start": 8.572306871414185, "end": **********.074575, "relative_end": **********.074575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::15422be7d0f2aaf8c8244c1e9db20ad9", "start": **********.076524, "relative_start": 8.57425594329834, "end": **********.076524, "relative_end": **********.076524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.077174, "relative_start": 8.57490587234497, "end": **********.077174, "relative_end": **********.077174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2e5e965add6d0ee3aadb02ca38e70825", "start": **********.079032, "relative_start": 8.57676386833191, "end": **********.079032, "relative_end": **********.079032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.079617, "relative_start": 8.577348947525024, "end": **********.079617, "relative_end": **********.079617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5b8d99843e0f8eff6046d7236026b187", "start": **********.081459, "relative_start": 8.579190969467163, "end": **********.081459, "relative_end": **********.081459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.082045, "relative_start": 8.579777002334595, "end": **********.082045, "relative_end": **********.082045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.08391, "relative_start": 8.581641912460327, "end": **********.08391, "relative_end": **********.08391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.0845, "relative_start": 8.582231998443604, "end": **********.0845, "relative_end": **********.0845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "start": **********.086324, "relative_start": 8.58405590057373, "end": **********.086324, "relative_end": **********.086324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.08692, "relative_start": 8.584651947021484, "end": **********.08692, "relative_end": **********.08692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.087597, "relative_start": 8.585328817367554, "end": **********.087597, "relative_end": **********.087597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "start": **********.08847, "relative_start": 8.586201906204224, "end": **********.08847, "relative_end": **********.08847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.089508, "relative_start": 8.587239980697632, "end": **********.089508, "relative_end": **********.089508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.090395, "relative_start": 8.58812689781189, "end": **********.090395, "relative_end": **********.090395, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dac323985d9d2618ad252313442aaf03", "start": **********.092766, "relative_start": 8.590497970581055, "end": **********.092766, "relative_end": **********.092766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.093391, "relative_start": 8.59112286567688, "end": **********.093391, "relative_end": **********.093391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::298cd8a12b86a6f371ff06491a0822fa", "start": **********.094764, "relative_start": 8.592495918273926, "end": **********.094764, "relative_end": **********.094764, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.095285, "relative_start": 8.593016862869263, "end": **********.095285, "relative_end": **********.095285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c59870a61b233f0766e3260625bdb025", "start": **********.09639, "relative_start": 8.594121932983398, "end": **********.09639, "relative_end": **********.09639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.096876, "relative_start": 8.594607830047607, "end": **********.096876, "relative_end": **********.096876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3890eca5d46a147ef99ddf994453d2ec", "start": **********.098038, "relative_start": 8.595769882202148, "end": **********.098038, "relative_end": **********.098038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.098525, "relative_start": 8.596256971359253, "end": **********.098525, "relative_end": **********.098525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff1fde71531b073b2c658173537aaea5", "start": **********.099635, "relative_start": 8.59736680984497, "end": **********.099635, "relative_end": **********.099635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.100145, "relative_start": 8.597877025604248, "end": **********.100145, "relative_end": **********.100145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "start": **********.101377, "relative_start": 8.599108934402466, "end": **********.101377, "relative_end": **********.101377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.101888, "relative_start": 8.59961986541748, "end": **********.101888, "relative_end": **********.101888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::89cb89d3fdb0a0a12f8aa61073c231e6", "start": **********.102989, "relative_start": 8.600720882415771, "end": **********.102989, "relative_end": **********.102989, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.103499, "relative_start": 8.60123085975647, "end": **********.103499, "relative_end": **********.103499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5a5b09d3f2ee0ddb2b536feb532d230a", "start": **********.104716, "relative_start": 8.602447986602783, "end": **********.104716, "relative_end": **********.104716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.105518, "relative_start": 8.60325002670288, "end": **********.105518, "relative_end": **********.105518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fedc652debeb23dcbb31a98830baa397", "start": **********.107152, "relative_start": 8.604883909225464, "end": **********.107152, "relative_end": **********.107152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.107814, "relative_start": 8.605545997619629, "end": **********.107814, "relative_end": **********.107814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.108452, "relative_start": 8.606184005737305, "end": **********.108452, "relative_end": **********.108452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ff3b2cf4e42e74e63db76ff05c5f2374", "start": **********.110405, "relative_start": 8.608136892318726, "end": **********.110405, "relative_end": **********.110405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.110951, "relative_start": 8.608682870864868, "end": **********.110951, "relative_end": **********.110951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d5f951b8390a2ab38dcc695dab7b152", "start": **********.112108, "relative_start": 8.609839916229248, "end": **********.112108, "relative_end": **********.112108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.112639, "relative_start": 8.610370874404907, "end": **********.112639, "relative_end": **********.112639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::944e2ad490e8085f4039835ea5eefcbf", "start": **********.113804, "relative_start": 8.611536026000977, "end": **********.113804, "relative_end": **********.113804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.114321, "relative_start": 8.612052917480469, "end": **********.114321, "relative_end": **********.114321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.114931, "relative_start": 8.61266303062439, "end": **********.114931, "relative_end": **********.114931, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::745871da7c635a3f461dfaeeef54a48e", "start": **********.116077, "relative_start": 8.613808870315552, "end": **********.116077, "relative_end": **********.116077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.116623, "relative_start": 8.614354848861694, "end": **********.116623, "relative_end": **********.116623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d6f928aaf1e585d3246cb3bee8fdd45", "start": **********.117972, "relative_start": 8.615703821182251, "end": **********.117972, "relative_end": **********.117972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.118454, "relative_start": 8.616185903549194, "end": **********.118454, "relative_end": **********.118454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.119073, "relative_start": 8.616804838180542, "end": **********.119073, "relative_end": **********.119073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::2b3233eda7e50501ef45fd875b12da49", "start": **********.120215, "relative_start": 8.617946863174438, "end": **********.120215, "relative_end": **********.120215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item", "start": **********.120974, "relative_start": 8.618705987930298, "end": **********.120974, "relative_end": **********.120974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.navbar-nav-item-link", "start": **********.121586, "relative_start": 8.619318008422852, "end": **********.121586, "relative_end": **********.121586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b13663c834a4ae876ef8f72aa0610e8c", "start": **********.122737, "relative_start": 8.620468854904175, "end": **********.122737, "relative_end": **********.122737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.page-header", "start": **********.123575, "relative_start": 8.621306896209717, "end": **********.123575, "relative_end": **********.123575, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::breadcrumb", "start": **********.124092, "relative_start": 8.621824026107788, "end": **********.124092, "relative_end": **********.124092, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.partials.footer", "start": **********.125153, "relative_start": 8.62288498878479, "end": **********.125153, "relative_end": **********.125153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::partials.copyright", "start": **********.125609, "relative_start": 8.623340845108032, "end": **********.125609, "relative_end": **********.125609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::layouts.vertical.partials.after-content", "start": **********.126591, "relative_start": 8.624322891235352, "end": **********.126591, "relative_end": **********.126591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::global-search.form", "start": **********.127137, "relative_start": 8.624868869781494, "end": **********.127137, "relative_end": **********.127137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::3cec1c87224222bda738c53f782c5bc1", "start": **********.128665, "relative_start": 8.626396894454956, "end": **********.128665, "relative_end": **********.128665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.128934, "relative_start": 8.626665830612183, "end": **********.128934, "relative_end": **********.128934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.129664, "relative_start": 8.627395868301392, "end": **********.129664, "relative_end": **********.129664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.130026, "relative_start": 8.627758026123047, "end": **********.130026, "relative_end": **********.130026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.130323, "relative_start": 8.628054857254028, "end": **********.130323, "relative_end": **********.130323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.index", "start": **********.130655, "relative_start": 8.628386974334717, "end": **********.130655, "relative_end": **********.130655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::414e4e803eeec6389552bb46515583c5", "start": **********.131548, "relative_start": 8.629279851913452, "end": **********.131548, "relative_end": **********.131548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::c47a448d99d5719cb034f7947c739ff8", "start": **********.132317, "relative_start": 8.630048990249634, "end": **********.132317, "relative_end": **********.132317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::e226165e1fca7eccb10f6857d7cd235a", "start": **********.133072, "relative_start": 8.63080382347107, "end": **********.133072, "relative_end": **********.133072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.133344, "relative_start": 8.631075859069824, "end": **********.133344, "relative_end": **********.133344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::custom-template", "start": **********.134134, "relative_start": 8.631865978240967, "end": **********.134134, "relative_end": **********.134134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::partials.media", "start": **********.134712, "relative_start": 8.632443904876709, "end": **********.134712, "relative_end": **********.134712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.135442, "relative_start": 8.633173942565918, "end": **********.135442, "relative_end": **********.135442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::loading", "start": **********.135671, "relative_start": 8.633402824401855, "end": **********.135671, "relative_end": **********.135671, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.text-input", "start": **********.13613, "relative_start": 8.633862018585205, "end": **********.13613, "relative_end": **********.13613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.label", "start": **********.136897, "relative_start": 8.634629011154175, "end": **********.136897, "relative_end": **********.136897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.error", "start": **********.137269, "relative_start": 8.635000944137573, "end": **********.137269, "relative_end": **********.137269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form-group", "start": **********.137586, "relative_start": 8.635318040847778, "end": **********.137586, "relative_end": **********.137586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::form.checkbox", "start": **********.137962, "relative_start": 8.635694026947021, "end": **********.137962, "relative_end": **********.137962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.139109, "relative_start": 8.6368408203125, "end": **********.139109, "relative_end": **********.139109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::button", "start": **********.139697, "relative_start": 8.637428998947144, "end": **********.139697, "relative_end": **********.139697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.140368, "relative_start": 8.638099908828735, "end": **********.140368, "relative_end": **********.140368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.141106, "relative_start": 8.638837814331055, "end": **********.141106, "relative_end": **********.141106, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/media::config", "start": **********.141574, "relative_start": 8.639305830001831, "end": **********.141574, "relative_end": **********.141574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::debug-badge", "start": **********.354096, "relative_start": 8.85182785987854, "end": **********.354096, "relative_end": **********.354096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.354641, "relative_start": 8.852372884750366, "end": **********.354641, "relative_end": **********.354641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.355287, "relative_start": 8.853018999099731, "end": **********.355287, "relative_end": **********.355287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.355902, "relative_start": 8.853633880615234, "end": **********.355902, "relative_end": **********.355902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::dcf17957c4aa053a618fd9c312cc29fc", "start": **********.356227, "relative_start": 8.85395884513855, "end": **********.356227, "relative_end": **********.356227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.356479, "relative_start": 8.85421085357666, "end": **********.356479, "relative_end": **********.356479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.action", "start": **********.357397, "relative_start": 8.85512900352478, "end": **********.357397, "relative_end": **********.357397, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.alert", "start": **********.358089, "relative_start": 8.855820894241333, "end": **********.358089, "relative_end": **********.358089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal.close-button", "start": **********.358963, "relative_start": 8.85669493675232, "end": **********.358963, "relative_end": **********.358963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fcfb9f9ba5fb460899f38b71f491e1fe", "start": **********.3602, "relative_start": 8.857931852340698, "end": **********.3602, "relative_end": **********.3602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::modal", "start": **********.360494, "relative_start": 8.85822582244873, "end": **********.360494, "relative_end": **********.360494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: ********************************::layouts.base", "start": **********.36149, "relative_start": 8.859221935272217, "end": **********.36149, "relative_end": **********.36149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::components.layouts.header", "start": **********.362312, "relative_start": 8.860044002532959, "end": **********.362312, "relative_end": **********.362312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::header", "start": **********.364641, "relative_start": 8.862372875213623, "end": **********.364641, "relative_end": **********.364641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::elements.common", "start": **********.366634, "relative_start": 8.864365816116333, "end": **********.366634, "relative_end": **********.366634, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: assets::footer", "start": **********.36836, "relative_start": 8.866091966629028, "end": **********.36836, "relative_end": **********.36836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/base::notification.notification", "start": **********.369327, "relative_start": 8.867058992385864, "end": **********.369327, "relative_end": **********.369327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.370352, "relative_start": 8.868083953857422, "end": **********.371059, "relative_end": **********.371059, "duration": 0.0007069110870361328, "duration_str": "707μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 71384384, "peak_usage_str": "68MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 560, "nb_templates": 560, "templates": [{"name": "1x plugins/ecommerce::customers.addresses.addresses", "param_count": null, "params": [], "start": **********.382588, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/views/customers/addresses/addresses.blade.phpplugins/ecommerce::customers.addresses.addresses", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcustomers%2Faddresses%2Faddresses.blade.php&line=1", "ajax": false, "filename": "addresses.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::customers.addresses.addresses"}, {"name": "16x ********************************::table.header.cell", "param_count": null, "params": [], "start": **********.183579, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/table/header/cell.blade.php********************************::table.header.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 16, "name_original": "********************************::table.header.cell"}, {"name": "3x ********************************::table.header.index", "param_count": null, "params": [], "start": **********.186685, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/table/header/index.blade.php********************************::table.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::table.header.index"}, {"name": "3x ********************************::table.body.cell", "param_count": null, "params": [], "start": **********.187361, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/table/body/cell.blade.php********************************::table.body.cell", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Fcell.blade.php&line=1", "ajax": false, "filename": "cell.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::table.body.cell"}, {"name": "3x ********************************::table.body.row", "param_count": null, "params": [], "start": **********.187867, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/table/body/row.blade.php********************************::table.body.row", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Frow.blade.php&line=1", "ajax": false, "filename": "row.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::table.body.row"}, {"name": "3x ********************************::table.body.index", "param_count": null, "params": [], "start": **********.188388, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/table/body/index.blade.php********************************::table.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::table.body.index"}, {"name": "3x ********************************::table.index", "param_count": null, "params": [], "start": **********.188921, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/table/index.blade.php********************************::table.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftable%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::table.index"}, {"name": "1x plugins/ecommerce::customers.addresses.address-actions", "param_count": null, "params": [], "start": **********.189832, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/views/customers/addresses/address-actions.blade.phpplugins/ecommerce::customers.addresses.address-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcustomers%2Faddresses%2Faddress-actions.blade.php&line=1", "ajax": false, "filename": "address-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::customers.addresses.address-actions"}, {"name": "20x ********************************::button", "param_count": null, "params": [], "start": **********.335435, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/button.blade.php********************************::button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 20, "name_original": "********************************::button"}, {"name": "1x __components::d9cf6f2adf46159095e24cd6ce9ab42b", "param_count": null, "params": [], "start": **********.338867, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d9cf6f2adf46159095e24cd6ce9ab42b.blade.php__components::d9cf6f2adf46159095e24cd6ce9ab42b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd9cf6f2adf46159095e24cd6ce9ab42b.blade.php&line=1", "ajax": false, "filename": "d9cf6f2adf46159095e24cd6ce9ab42b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::d9cf6f2adf46159095e24cd6ce9ab42b"}, {"name": "1x plugins/ecommerce::customers.wishlist", "param_count": null, "params": [], "start": **********.339448, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/views/customers/wishlist.blade.phpplugins/ecommerce::customers.wishlist", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcustomers%2Fwishlist.blade.php&line=1", "ajax": false, "filename": "wishlist.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::customers.wishlist"}, {"name": "1x plugins/ecommerce::customers.payments.payments", "param_count": null, "params": [], "start": 1749846439.484519, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/views/customers/payments/payments.blade.phpplugins/ecommerce::customers.payments.payments", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcustomers%2Fpayments%2Fpayments.blade.php&line=1", "ajax": false, "filename": "payments.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::customers.payments.payments"}, {"name": "4x ********************************::modal.action", "param_count": null, "params": [], "start": 1749846441.630437, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal/action.blade.php********************************::modal.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.action"}, {"name": "4x ********************************::modal.alert", "param_count": null, "params": [], "start": 1749846441.631582, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal/alert.blade.php********************************::modal.alert", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::modal.alert"}, {"name": "9x ********************************::modal.close-button", "param_count": null, "params": [], "start": 1749846441.632473, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal/close-button.blade.php********************************::modal.close-button", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal%2Fclose-button.blade.php&line=1", "ajax": false, "filename": "close-button.blade.php", "line": "?"}, "render_count": 9, "name_original": "********************************::modal.close-button"}, {"name": "2x __components::dcf17957c4aa053a618fd9c312cc29fc", "param_count": null, "params": [], "start": 1749846441.633414, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dcf17957c4aa053a618fd9c312cc29fc.blade.php__components::dcf17957c4aa053a618fd9c312cc29fc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdcf17957c4aa053a618fd9c312cc29fc.blade.php&line=1", "ajax": false, "filename": "dcf17957c4aa053a618fd9c312cc29fc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::dcf17957c4aa053a618fd9c312cc29fc"}, {"name": "9x ********************************::modal", "param_count": null, "params": [], "start": 1749846441.633732, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/modal.blade.php********************************::modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fmodal.blade.php&line=1", "ajax": false, "filename": "modal.blade.php", "line": "?"}, "render_count": 9, "name_original": "********************************::modal"}, {"name": "1x plugins/marketplace::customers.control", "param_count": null, "params": [], "start": 1749846441.636994, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/customers/control.blade.phpplugins/marketplace::customers.control", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fcustomers%2Fcontrol.blade.php&line=1", "ajax": false, "filename": "control.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::customers.control"}, {"name": "1x plugins/ecommerce::customers.form", "param_count": null, "params": [], "start": 1749846441.823988, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/views/customers/form.blade.phpplugins/ecommerce::customers.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fcustomers%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/ecommerce::customers.form"}, {"name": "1x core/base::forms.form-content-only", "param_count": null, "params": [], "start": **********.140042, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/form-content-only.blade.phpcore/base::forms.form-content-only", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-content-only.blade.php&line=1", "ajax": false, "filename": "form-content-only.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-content-only"}, {"name": "2x core/base::forms.columns.form-open-wrapper", "param_count": null, "params": [], "start": **********.1411, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/columns/form-open-wrapper.blade.phpcore/base::forms.columns.form-open-wrapper", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fform-open-wrapper.blade.php&line=1", "ajax": false, "filename": "form-open-wrapper.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.columns.form-open-wrapper"}, {"name": "4x core/base::forms.fields.html", "param_count": null, "params": [], "start": **********.142149, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/html.blade.phpcore/base::forms.fields.html", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fhtml.blade.php&line=1", "ajax": false, "filename": "html.blade.php", "line": "?"}, "render_count": 4, "name_original": "core/base::forms.fields.html"}, {"name": "20x ********************************::form.field", "param_count": null, "params": [], "start": **********.142907, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/field.blade.php********************************::form.field", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 20, "name_original": "********************************::form.field"}, {"name": "20x core/base::forms.partials.help-block", "param_count": null, "params": [], "start": **********.143686, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/help-block.blade.phpcore/base::forms.partials.help-block", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fhelp-block.blade.php&line=1", "ajax": false, "filename": "help-block.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.partials.help-block"}, {"name": "20x core/base::forms.partials.errors", "param_count": null, "params": [], "start": **********.144135, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/errors.blade.phpcore/base::forms.partials.errors", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Ferrors.blade.php&line=1", "ajax": false, "filename": "errors.blade.php", "line": "?"}, "render_count": 20, "name_original": "core/base::forms.partials.errors"}, {"name": "23x core/base::forms.columns.column-span", "param_count": null, "params": [], "start": **********.145554, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/columns/column-span.blade.phpcore/base::forms.columns.column-span", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fcolumns%2Fcolumn-span.blade.php&line=1", "ajax": false, "filename": "column-span.blade.php", "line": "?"}, "render_count": 23, "name_original": "core/base::forms.columns.column-span"}, {"name": "8x core/base::forms.fields.text", "param_count": null, "params": [], "start": **********.146092, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/text.blade.phpcore/base::forms.fields.text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftext.blade.php&line=1", "ajax": false, "filename": "text.blade.php", "line": "?"}, "render_count": 8, "name_original": "core/base::forms.fields.text"}, {"name": "16x core/base::forms.partials.label", "param_count": null, "params": [], "start": **********.146686, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/label.blade.phpcore/base::forms.partials.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 16, "name_original": "core/base::forms.partials.label"}, {"name": "19x ********************************::form.label", "param_count": null, "params": [], "start": **********.147239, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/label.blade.php********************************::form.label", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Flabel.blade.php&line=1", "ajax": false, "filename": "label.blade.php", "line": "?"}, "render_count": 19, "name_original": "********************************::form.label"}, {"name": "1x core/base::forms.fields.email", "param_count": null, "params": [], "start": **********.151603, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/email.blade.phpcore/base::forms.fields.email", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Femail.blade.php&line=1", "ajax": false, "filename": "email.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.email"}, {"name": "1x core/base::forms.fields.custom-select", "param_count": null, "params": [], "start": **********.154427, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/custom-select.blade.phpcore/base::forms.fields.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.custom-select"}, {"name": "1x core/base::forms.partials.custom-select", "param_count": null, "params": [], "start": **********.155922, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/custom-select.blade.phpcore/base::forms.partials.custom-select", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fcustom-select.blade.php&line=1", "ajax": false, "filename": "custom-select.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.custom-select"}, {"name": "1x core/base::forms.fields.checkbox", "param_count": null, "params": [], "start": **********.16594, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/checkbox.blade.phpcore/base::forms.fields.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.checkbox"}, {"name": "2x ********************************::form.checkbox", "param_count": null, "params": [], "start": **********.353958, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/checkbox.blade.php********************************::form.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.checkbox"}, {"name": "3x laravel-form-builder::text", "param_count": null, "params": [], "start": **********.360057, "type": "php", "hash": "phpD:\\laragon\\www\\muhrak\\vendor\\botble\\form-builder\\src/../resources/views/text.phplaravel-form-builder::text", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fform-builder%2Fresources%2Fviews%2Ftext.php&line=1", "ajax": false, "filename": "text.php", "line": "?"}, "render_count": 3, "name_original": "laravel-form-builder::text"}, {"name": "2x core/js-validation::bootstrap", "param_count": null, "params": [], "start": **********.422592, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/js-validation/resources/views/bootstrap.blade.phpcore/js-validation::bootstrap", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fjs-validation%2Fresources%2Fviews%2Fbootstrap.blade.php&line=1", "ajax": false, "filename": "bootstrap.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/js-validation::bootstrap"}, {"name": "2x ********************************::loading", "param_count": null, "params": [], "start": **********.426172, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/loading.blade.php********************************::loading", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Floading.blade.php&line=1", "ajax": false, "filename": "loading.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::loading"}, {"name": "1x plugins/marketplace::customers.control-modal", "param_count": null, "params": [], "start": **********.431626, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/marketplace/resources/views/customers/control-modal.blade.phpplugins/marketplace::customers.control-modal", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fresources%2Fviews%2Fcustomers%2Fcontrol-modal.blade.php&line=1", "ajax": false, "filename": "control-modal.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/marketplace::customers.control-modal"}, {"name": "1x __components::b61dc00cf92742620aec4bf35aea701a", "param_count": null, "params": [], "start": **********.732244, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b61dc00cf92742620aec4bf35aea701a.blade.php__components::b61dc00cf92742620aec4bf35aea701a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb61dc00cf92742620aec4bf35aea701a.blade.php&line=1", "ajax": false, "filename": "b61dc00cf92742620aec4bf35aea701a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b61dc00cf92742620aec4bf35aea701a"}, {"name": "10x ********************************::card.header.index", "param_count": null, "params": [], "start": **********.734027, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/header/index.blade.php********************************::card.header.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::card.header.index"}, {"name": "1x core/table::bulk-changes", "param_count": null, "params": [], "start": **********.749688, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-changes"}, {"name": "7x ********************************::dropdown.item", "param_count": null, "params": [], "start": **********.750377, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.php********************************::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::dropdown.item"}, {"name": "1x __components::b3952f2f8079cae92614235bd0a03d56", "param_count": null, "params": [], "start": **********.75151, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b3952f2f8079cae92614235bd0a03d56.blade.php__components::b3952f2f8079cae92614235bd0a03d56", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb3952f2f8079cae92614235bd0a03d56.blade.php&line=1", "ajax": false, "filename": "b3952f2f8079cae92614235bd0a03d56.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3952f2f8079cae92614235bd0a03d56"}, {"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.754082, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "2x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.754977, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x ********************************::badge", "param_count": null, "params": [], "start": **********.755442, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/badge.blade.php********************************::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::badge"}, {"name": "1x core/table::simple-table", "param_count": null, "params": [], "start": **********.763536, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/simple-table.blade.phpcore/table::simple-table", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fsimple-table.blade.php&line=1", "ajax": false, "filename": "simple-table.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::simple-table"}, {"name": "10x ********************************::card.index", "param_count": null, "params": [], "start": **********.765, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/index.blade.php********************************::card.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::card.index"}, {"name": "1x datatables::script", "param_count": null, "params": [], "start": **********.766978, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-html\\src/resources/views/script.blade.phpdatatables::script", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fyajra%2Flaravel-datatables-html%2Fsrc%2Fresources%2Fviews%2Fscript.blade.php&line=1", "ajax": false, "filename": "script.blade.php", "line": "?"}, "render_count": 1, "name_original": "datatables::script"}, {"name": "1x core/base::forms.form-tabs", "param_count": null, "params": [], "start": **********.767766, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/form-tabs.blade.phpcore/base::forms.form-tabs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fform-tabs.blade.php&line=1", "ajax": false, "filename": "form-tabs.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.form-tabs"}, {"name": "1x ********************************::tab.item", "param_count": null, "params": [], "start": **********.771342, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/tab/item.blade.php********************************::tab.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftab%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::tab.item"}, {"name": "1x ********************************::tab.index", "param_count": null, "params": [], "start": **********.772305, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/tab/index.blade.php********************************::tab.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftab%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::tab.index"}, {"name": "2x core/base::forms.fields.on-off", "param_count": null, "params": [], "start": **********.779013, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/on-off.blade.phpcore/base::forms.fields.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.fields.on-off"}, {"name": "2x core/base::forms.partials.on-off", "param_count": null, "params": [], "start": **********.779534, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/on-off.blade.phpcore/base::forms.partials.on-off", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fon-off.blade.php&line=1", "ajax": false, "filename": "on-off.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.on-off"}, {"name": "2x ********************************::form.toggle", "param_count": null, "params": [], "start": **********.780051, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/toggle.blade.php********************************::form.toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftoggle.blade.php&line=1", "ajax": false, "filename": "toggle.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::form.toggle"}, {"name": "1x core/base::forms.fields.date-picker", "param_count": null, "params": [], "start": **********.783993, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/date-picker.blade.phpcore/base::forms.fields.date-picker", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fdate-picker.blade.php&line=1", "ajax": false, "filename": "date-picker.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.date-picker"}, {"name": "1x core/base::forms.partials.date-picker", "param_count": null, "params": [], "start": **********.785536, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/date-picker.blade.phpcore/base::forms.partials.date-picker", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fdate-picker.blade.php&line=1", "ajax": false, "filename": "date-picker.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.date-picker"}, {"name": "1x __components::71e661e6e74c892ee0d38d1089b1cb44", "param_count": null, "params": [], "start": **********.787449, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/71e661e6e74c892ee0d38d1089b1cb44.blade.php__components::71e661e6e74c892ee0d38d1089b1cb44", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F71e661e6e74c892ee0d38d1089b1cb44.blade.php&line=1", "ajax": false, "filename": "71e661e6e74c892ee0d38d1089b1cb44.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::71e661e6e74c892ee0d38d1089b1cb44"}, {"name": "1x __components::b9129d3adbe29512c27ed8c014c531f8", "param_count": null, "params": [], "start": **********.79081, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b9129d3adbe29512c27ed8c014c531f8.blade.php__components::b9129d3adbe29512c27ed8c014c531f8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb9129d3adbe29512c27ed8c014c531f8.blade.php&line=1", "ajax": false, "filename": "b9129d3adbe29512c27ed8c014c531f8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b9129d3adbe29512c27ed8c014c531f8"}, {"name": "1x core/base::forms.fields.textarea", "param_count": null, "params": [], "start": **********.823188, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/textarea.blade.phpcore/base::forms.fields.textarea", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Ftextarea.blade.php&line=1", "ajax": false, "filename": "textarea.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.textarea"}, {"name": "1x ********************************::tab.pane", "param_count": null, "params": [], "start": **********.825855, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/tab/pane.blade.php********************************::tab.pane", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftab%2Fpane.blade.php&line=1", "ajax": false, "filename": "pane.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::tab.pane"}, {"name": "1x ********************************::tab.content", "param_count": null, "params": [], "start": **********.826966, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/tab/content.blade.php********************************::tab.content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Ftab%2Fcontent.blade.php&line=1", "ajax": false, "filename": "content.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::tab.content"}, {"name": "4x ********************************::card.body.index", "param_count": null, "params": [], "start": **********.827483, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/body/index.blade.php********************************::card.body.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Fbody%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 4, "name_original": "********************************::card.body.index"}, {"name": "3x core/base::forms.partials.meta-box", "param_count": null, "params": [], "start": **********.828205, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/meta-box.blade.phpcore/base::forms.partials.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::forms.partials.meta-box"}, {"name": "7x ********************************::card.title", "param_count": null, "params": [], "start": **********.829375, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/title.blade.php********************************::card.title", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Ftitle.blade.php&line=1", "ajax": false, "filename": "title.blade.php", "line": "?"}, "render_count": 7, "name_original": "********************************::card.title"}, {"name": "2x ********************************::card.actions", "param_count": null, "params": [], "start": **********.829859, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/card/actions.blade.php********************************::card.actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcard%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::card.actions"}, {"name": "3x core/base::elements.meta-box", "param_count": null, "params": [], "start": **********.836763, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/elements/meta-box.blade.phpcore/base::elements.meta-box", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fmeta-box.blade.php&line=1", "ajax": false, "filename": "meta-box.blade.php", "line": "?"}, "render_count": 3, "name_original": "core/base::elements.meta-box"}, {"name": "1x core/base::forms.partials.form-actions", "param_count": null, "params": [], "start": **********.837298, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/form-actions.blade.phpcore/base::forms.partials.form-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-actions.blade.php&line=1", "ajax": false, "filename": "form-actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.form-actions"}, {"name": "2x core/base::forms.partials.form-buttons", "param_count": null, "params": [], "start": **********.83892, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/form-buttons.blade.phpcore/base::forms.partials.form-buttons", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fform-buttons.blade.php&line=1", "ajax": false, "filename": "form-buttons.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::forms.partials.form-buttons"}, {"name": "2x __components::d25e365b684c7fa9ad5ec13cfb768fc1", "param_count": null, "params": [], "start": **********.840942, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d25e365b684c7fa9ad5ec13cfb768fc1.blade.php__components::d25e365b684c7fa9ad5ec13cfb768fc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd25e365b684c7fa9ad5ec13cfb768fc1.blade.php&line=1", "ajax": false, "filename": "d25e365b684c7fa9ad5ec13cfb768fc1.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::d25e365b684c7fa9ad5ec13cfb768fc1"}, {"name": "2x __components::5db12cde11beb11dd9ef0499874ec197", "param_count": null, "params": [], "start": **********.842388, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5db12cde11beb11dd9ef0499874ec197.blade.php__components::5db12cde11beb11dd9ef0499874ec197", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5db12cde11beb11dd9ef0499874ec197.blade.php&line=1", "ajax": false, "filename": "5db12cde11beb11dd9ef0499874ec197.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::5db12cde11beb11dd9ef0499874ec197"}, {"name": "1x core/base::layouts.partials.breadcrumbs", "param_count": null, "params": [], "start": **********.846295, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/breadcrumbs.blade.phpcore/base::layouts.partials.breadcrumbs", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fbreadcrumbs.blade.php&line=1", "ajax": false, "filename": "breadcrumbs.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.breadcrumbs"}, {"name": "1x core/base::forms.fields.media-image", "param_count": null, "params": [], "start": **********.855962, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/fields/media-image.blade.phpcore/base::forms.fields.media-image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Ffields%2Fmedia-image.blade.php&line=1", "ajax": false, "filename": "media-image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.fields.media-image"}, {"name": "1x core/base::forms.partials.image", "param_count": null, "params": [], "start": **********.856728, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/forms/partials/image.blade.phpcore/base::forms.partials.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fforms%2Fpartials%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::forms.partials.image"}, {"name": "1x ********************************::form.image", "param_count": null, "params": [], "start": **********.857516, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/image.blade.php********************************::form.image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.image"}, {"name": "1x ********************************::image", "param_count": null, "params": [], "start": **********.870477, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/image.blade.php********************************::image", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fimage.blade.php&line=1", "ajax": false, "filename": "image.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::image"}, {"name": "1x __components::1c3e3c2b5f3306f2c083e21b9339fe63", "param_count": null, "params": [], "start": **********.871986, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1c3e3c2b5f3306f2c083e21b9339fe63.blade.php__components::1c3e3c2b5f3306f2c083e21b9339fe63", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1c3e3c2b5f3306f2c083e21b9339fe63.blade.php&line=1", "ajax": false, "filename": "1c3e3c2b5f3306f2c083e21b9339fe63.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1c3e3c2b5f3306f2c083e21b9339fe63"}, {"name": "1x core/base::layouts.master", "param_count": null, "params": [], "start": **********.88301, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/master.blade.phpcore/base::layouts.master", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.master"}, {"name": "1x core/base::layouts.vertical.partials.before-content", "param_count": null, "params": [], "start": **********.883893, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/before-content.blade.phpcore/base::layouts.vertical.partials.before-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fbefore-content.blade.php&line=1", "ajax": false, "filename": "before-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.before-content"}, {"name": "1x core/base::layouts.vertical.partials.header", "param_count": null, "params": [], "start": **********.884311, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/header.blade.phpcore/base::layouts.vertical.partials.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.header"}, {"name": "2x __components::133aa97c11fca0f84f02ebcb9fd067dc", "param_count": null, "params": [], "start": **********.885535, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/133aa97c11fca0f84f02ebcb9fd067dc.blade.php__components::133aa97c11fca0f84f02ebcb9fd067dc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F133aa97c11fca0f84f02ebcb9fd067dc.blade.php&line=1", "ajax": false, "filename": "133aa97c11fca0f84f02ebcb9fd067dc.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::133aa97c11fca0f84f02ebcb9fd067dc"}, {"name": "2x core/base::partials.logo", "param_count": null, "params": [], "start": **********.885915, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/partials/logo.blade.phpcore/base::partials.logo", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/base::partials.logo"}, {"name": "1x core/base::global-search.navbar-input", "param_count": null, "params": [], "start": **********.886459, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/global-search/navbar-input.blade.phpcore/base::global-search.navbar-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fnavbar-input.blade.php&line=1", "ajax": false, "filename": "navbar-input.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.navbar-input"}, {"name": "3x ********************************::form.text-input", "param_count": null, "params": [], "start": **********.886987, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/text-input.blade.php********************************::form.text-input", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ftext-input.blade.php&line=1", "ajax": false, "filename": "text-input.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form.text-input"}, {"name": "3x ********************************::form.error", "param_count": null, "params": [], "start": **********.888363, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/error.blade.php********************************::form.error", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Ferror.blade.php&line=1", "ajax": false, "filename": "error.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form.error"}, {"name": "3x ********************************::form-group", "param_count": null, "params": [], "start": **********.888787, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form-group.blade.php********************************::form-group", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform-group.blade.php&line=1", "ajax": false, "filename": "form-group.blade.php", "line": "?"}, "render_count": 3, "name_original": "********************************::form-group"}, {"name": "1x __components::dc988bb05638c97d86c3bc8a9b727e31", "param_count": null, "params": [], "start": **********.890963, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dc988bb05638c97d86c3bc8a9b727e31.blade.php__components::dc988bb05638c97d86c3bc8a9b727e31", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdc988bb05638c97d86c3bc8a9b727e31.blade.php&line=1", "ajax": false, "filename": "dc988bb05638c97d86c3bc8a9b727e31.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dc988bb05638c97d86c3bc8a9b727e31"}, {"name": "1x core/base::layouts.partials.theme-toggle", "param_count": null, "params": [], "start": **********.891337, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/theme-toggle.blade.phpcore/base::layouts.partials.theme-toggle", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ftheme-toggle.blade.php&line=1", "ajax": false, "filename": "theme-toggle.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.theme-toggle"}, {"name": "1x __components::ec711b4f47c4b42ebc81a7564c1d8d33", "param_count": null, "params": [], "start": **********.892533, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ec711b4f47c4b42ebc81a7564c1d8d33.blade.php__components::ec711b4f47c4b42ebc81a7564c1d8d33", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fec711b4f47c4b42ebc81a7564c1d8d33.blade.php&line=1", "ajax": false, "filename": "ec711b4f47c4b42ebc81a7564c1d8d33.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ec711b4f47c4b42ebc81a7564c1d8d33"}, {"name": "1x core/base::notification.nav-item", "param_count": null, "params": [], "start": **********.893799, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/notification/nav-item.blade.phpcore/base::notification.nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnav-item.blade.php&line=1", "ajax": false, "filename": "nav-item.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.nav-item"}, {"name": "1x __components::306e03d3dc5634ee2e82192f553c6f9b", "param_count": null, "params": [], "start": **********.894675, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/306e03d3dc5634ee2e82192f553c6f9b.blade.php__components::306e03d3dc5634ee2e82192f553c6f9b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F306e03d3dc5634ee2e82192f553c6f9b.blade.php&line=1", "ajax": false, "filename": "306e03d3dc5634ee2e82192f553c6f9b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::306e03d3dc5634ee2e82192f553c6f9b"}, {"name": "1x plugins/contact::partials.notification", "param_count": null, "params": [], "start": **********.897131, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/contact/resources/views/partials/notification.blade.phpplugins/contact::partials.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fcontact%2Fresources%2Fviews%2Fpartials%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "plugins/contact::partials.notification"}, {"name": "3x __components::98e88d58787b8dfeb6f0d1dc0a785cfd", "param_count": null, "params": [], "start": **********.89838, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php__components::98e88d58787b8dfeb6f0d1dc0a785cfd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php&line=1", "ajax": false, "filename": "98e88d58787b8dfeb6f0d1dc0a785cfd.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::98e88d58787b8dfeb6f0d1dc0a785cfd"}, {"name": "1x core/base::layouts.partials.user-menu", "param_count": null, "params": [], "start": **********.923773, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/user-menu.blade.phpcore/base::layouts.partials.user-menu", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.user-menu"}, {"name": "2x __components::2907df26a6102c24ab0c37391217b338", "param_count": null, "params": [], "start": **********.92985, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2907df26a6102c24ab0c37391217b338.blade.php__components::2907df26a6102c24ab0c37391217b338", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2907df26a6102c24ab0c37391217b338.blade.php&line=1", "ajax": false, "filename": "2907df26a6102c24ab0c37391217b338.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::2907df26a6102c24ab0c37391217b338"}, {"name": "2x __components::f38bffca7b8a1a50e97a6950ffd66c5c", "param_count": null, "params": [], "start": **********.931156, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/f38bffca7b8a1a50e97a6950ffd66c5c.blade.php__components::f38bffca7b8a1a50e97a6950ffd66c5c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ff38bffca7b8a1a50e97a6950ffd66c5c.blade.php&line=1", "ajax": false, "filename": "f38bffca7b8a1a50e97a6950ffd66c5c.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::f38bffca7b8a1a50e97a6950ffd66c5c"}, {"name": "2x ********************************::dropdown.index", "param_count": null, "params": [], "start": **********.931449, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/index.blade.php********************************::dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "********************************::dropdown.index"}, {"name": "1x core/base::layouts.vertical.partials.aside", "param_count": null, "params": [], "start": **********.932124, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/aside.blade.phpcore/base::layouts.vertical.partials.aside", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Faside.blade.php&line=1", "ajax": false, "filename": "aside.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.aside"}, {"name": "1x core/base::layouts.vertical.partials.sidebar", "param_count": null, "params": [], "start": **********.936539, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/sidebar.blade.phpcore/base::layouts.vertical.partials.sidebar", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fsidebar.blade.php&line=1", "ajax": false, "filename": "sidebar.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.sidebar"}, {"name": "1x core/base::layouts.partials.navbar-nav", "param_count": null, "params": [], "start": **********.936957, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/navbar-nav.blade.phpcore/base::layouts.partials.navbar-nav", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav.blade.php&line=1", "ajax": false, "filename": "navbar-nav.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.navbar-nav"}, {"name": "18x core/base::layouts.partials.navbar-nav-item", "param_count": null, "params": [], "start": **********.954547, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/navbar-nav-item.blade.phpcore/base::layouts.partials.navbar-nav-item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item.blade.php", "line": "?"}, "render_count": 18, "name_original": "core/base::layouts.partials.navbar-nav-item"}, {"name": "75x core/base::layouts.partials.navbar-nav-item-link", "param_count": null, "params": [], "start": **********.955388, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/navbar-nav-item-link.blade.phpcore/base::layouts.partials.navbar-nav-item-link", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fnavbar-nav-item-link.blade.php&line=1", "ajax": false, "filename": "navbar-nav-item-link.blade.php", "line": "?"}, "render_count": 75, "name_original": "core/base::layouts.partials.navbar-nav-item-link"}, {"name": "1x __components::af68cda57c5ca67f3b8a7729953880bc", "param_count": null, "params": [], "start": **********.957379, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/af68cda57c5ca67f3b8a7729953880bc.blade.php__components::af68cda57c5ca67f3b8a7729953880bc", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Faf68cda57c5ca67f3b8a7729953880bc.blade.php&line=1", "ajax": false, "filename": "af68cda57c5ca67f3b8a7729953880bc.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::af68cda57c5ca67f3b8a7729953880bc"}, {"name": "1x __components::c50817c59f218fea1b6c19be4db347dd", "param_count": null, "params": [], "start": **********.960401, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c50817c59f218fea1b6c19be4db347dd.blade.php__components::c50817c59f218fea1b6c19be4db347dd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc50817c59f218fea1b6c19be4db347dd.blade.php&line=1", "ajax": false, "filename": "c50817c59f218fea1b6c19be4db347dd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c50817c59f218fea1b6c19be4db347dd"}, {"name": "10x core/base::partials.navbar.badge-count", "param_count": null, "params": [], "start": **********.961134, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/partials/navbar/badge-count.blade.phpcore/base::partials.navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 10, "name_original": "core/base::partials.navbar.badge-count"}, {"name": "10x ********************************::navbar.badge-count", "param_count": null, "params": [], "start": **********.961673, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/navbar/badge-count.blade.php********************************::navbar.badge-count", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fnavbar%2Fbadge-count.blade.php&line=1", "ajax": false, "filename": "badge-count.blade.php", "line": "?"}, "render_count": 10, "name_original": "********************************::navbar.badge-count"}, {"name": "1x __components::42d71f4fe12493bd6829a606380e1435", "param_count": null, "params": [], "start": **********.963919, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/42d71f4fe12493bd6829a606380e1435.blade.php__components::42d71f4fe12493bd6829a606380e1435", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F42d71f4fe12493bd6829a606380e1435.blade.php&line=1", "ajax": false, "filename": "42d71f4fe12493bd6829a606380e1435.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::42d71f4fe12493bd6829a606380e1435"}, {"name": "1x __components::1a65d1071503a2e05e73a706bfebac58", "param_count": null, "params": [], "start": **********.967115, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1a65d1071503a2e05e73a706bfebac58.blade.php__components::1a65d1071503a2e05e73a706bfebac58", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1a65d1071503a2e05e73a706bfebac58.blade.php&line=1", "ajax": false, "filename": "1a65d1071503a2e05e73a706bfebac58.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1a65d1071503a2e05e73a706bfebac58"}, {"name": "1x __components::7e33c8bf87ccc47b22832a4eb415ca9d", "param_count": null, "params": [], "start": **********.969553, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/7e33c8bf87ccc47b22832a4eb415ca9d.blade.php__components::7e33c8bf87ccc47b22832a4eb415ca9d", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F7e33c8bf87ccc47b22832a4eb415ca9d.blade.php&line=1", "ajax": false, "filename": "7e33c8bf87ccc47b22832a4eb415ca9d.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7e33c8bf87ccc47b22832a4eb415ca9d"}, {"name": "1x __components::452ccf81d5bbacec7ec062999540e293", "param_count": null, "params": [], "start": **********.971169, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/452ccf81d5bbacec7ec062999540e293.blade.php__components::452ccf81d5bbacec7ec062999540e293", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F452ccf81d5bbacec7ec062999540e293.blade.php&line=1", "ajax": false, "filename": "452ccf81d5bbacec7ec062999540e293.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::452ccf81d5bbacec7ec062999540e293"}, {"name": "1x __components::ee52ba22bc1262334c4146935fbed227", "param_count": null, "params": [], "start": **********.974567, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ee52ba22bc1262334c4146935fbed227.blade.php__components::ee52ba22bc1262334c4146935fbed227", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fee52ba22bc1262334c4146935fbed227.blade.php&line=1", "ajax": false, "filename": "ee52ba22bc1262334c4146935fbed227.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ee52ba22bc1262334c4146935fbed227"}, {"name": "1x __components::7bd0601fe0dc4645b269458a9ed5f144", "param_count": null, "params": [], "start": **********.976828, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/7bd0601fe0dc4645b269458a9ed5f144.blade.php__components::7bd0601fe0dc4645b269458a9ed5f144", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F7bd0601fe0dc4645b269458a9ed5f144.blade.php&line=1", "ajax": false, "filename": "7bd0601fe0dc4645b269458a9ed5f144.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::7bd0601fe0dc4645b269458a9ed5f144"}, {"name": "1x __components::5e1ed162565cf31bd543a8427caaef1e", "param_count": null, "params": [], "start": **********.978436, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5e1ed162565cf31bd543a8427caaef1e.blade.php__components::5e1ed162565cf31bd543a8427caaef1e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5e1ed162565cf31bd543a8427caaef1e.blade.php&line=1", "ajax": false, "filename": "5e1ed162565cf31bd543a8427caaef1e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5e1ed162565cf31bd543a8427caaef1e"}, {"name": "1x __components::e3de92deb18074b63f585befac7d0965", "param_count": null, "params": [], "start": **********.981061, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/e3de92deb18074b63f585befac7d0965.blade.php__components::e3de92deb18074b63f585befac7d0965", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fe3de92deb18074b63f585befac7d0965.blade.php&line=1", "ajax": false, "filename": "e3de92deb18074b63f585befac7d0965.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e3de92deb18074b63f585befac7d0965"}, {"name": "1x __components::69ab4d8cea5c292eefba375a85e9768a", "param_count": null, "params": [], "start": **********.983431, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/69ab4d8cea5c292eefba375a85e9768a.blade.php__components::69ab4d8cea5c292eefba375a85e9768a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F69ab4d8cea5c292eefba375a85e9768a.blade.php&line=1", "ajax": false, "filename": "69ab4d8cea5c292eefba375a85e9768a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::69ab4d8cea5c292eefba375a85e9768a"}, {"name": "1x __components::64f291ea9b97f679266b5e47f5ae8464", "param_count": null, "params": [], "start": **********.985007, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/64f291ea9b97f679266b5e47f5ae8464.blade.php__components::64f291ea9b97f679266b5e47f5ae8464", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F64f291ea9b97f679266b5e47f5ae8464.blade.php&line=1", "ajax": false, "filename": "64f291ea9b97f679266b5e47f5ae8464.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::64f291ea9b97f679266b5e47f5ae8464"}, {"name": "2x __components::9413e731e9bb6e320e018067130903e9", "param_count": null, "params": [], "start": **********.986599, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/9413e731e9bb6e320e018067130903e9.blade.php__components::9413e731e9bb6e320e018067130903e9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F9413e731e9bb6e320e018067130903e9.blade.php&line=1", "ajax": false, "filename": "9413e731e9bb6e320e018067130903e9.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::9413e731e9bb6e320e018067130903e9"}, {"name": "1x __components::5d3f310865558ef9371c1fb4b468d742", "param_count": null, "params": [], "start": **********.9882, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5d3f310865558ef9371c1fb4b468d742.blade.php__components::5d3f310865558ef9371c1fb4b468d742", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5d3f310865558ef9371c1fb4b468d742.blade.php&line=1", "ajax": false, "filename": "5d3f310865558ef9371c1fb4b468d742.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5d3f310865558ef9371c1fb4b468d742"}, {"name": "2x __components::59b84d05fdb5c77612cfb696a101e213", "param_count": null, "params": [], "start": **********.990776, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/59b84d05fdb5c77612cfb696a101e213.blade.php__components::59b84d05fdb5c77612cfb696a101e213", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F59b84d05fdb5c77612cfb696a101e213.blade.php&line=1", "ajax": false, "filename": "59b84d05fdb5c77612cfb696a101e213.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::59b84d05fdb5c77612cfb696a101e213"}, {"name": "1x __components::2c23a08a8a248b19ed43094ec82cfb6b", "param_count": null, "params": [], "start": **********.992911, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2c23a08a8a248b19ed43094ec82cfb6b.blade.php__components::2c23a08a8a248b19ed43094ec82cfb6b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2c23a08a8a248b19ed43094ec82cfb6b.blade.php&line=1", "ajax": false, "filename": "2c23a08a8a248b19ed43094ec82cfb6b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2c23a08a8a248b19ed43094ec82cfb6b"}, {"name": "1x __components::1859ae2c66dc0538fd83aadc7f316844", "param_count": null, "params": [], "start": **********.996391, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1859ae2c66dc0538fd83aadc7f316844.blade.php__components::1859ae2c66dc0538fd83aadc7f316844", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1859ae2c66dc0538fd83aadc7f316844.blade.php&line=1", "ajax": false, "filename": "1859ae2c66dc0538fd83aadc7f316844.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1859ae2c66dc0538fd83aadc7f316844"}, {"name": "1x __components::ccda1407f97c36756562e2c01f09a7ab", "param_count": null, "params": [], "start": **********.998105, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ccda1407f97c36756562e2c01f09a7ab.blade.php__components::ccda1407f97c36756562e2c01f09a7ab", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fccda1407f97c36756562e2c01f09a7ab.blade.php&line=1", "ajax": false, "filename": "ccda1407f97c36756562e2c01f09a7ab.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ccda1407f97c36756562e2c01f09a7ab"}, {"name": "1x __components::52309442cd989852b03aa65d96b55790", "param_count": null, "params": [], "start": **********.999832, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/52309442cd989852b03aa65d96b55790.blade.php__components::52309442cd989852b03aa65d96b55790", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F52309442cd989852b03aa65d96b55790.blade.php&line=1", "ajax": false, "filename": "52309442cd989852b03aa65d96b55790.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::52309442cd989852b03aa65d96b55790"}, {"name": "1x __components::2cd2bd3a1a36cafc00007578f96771de", "param_count": null, "params": [], "start": **********.003562, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2cd2bd3a1a36cafc00007578f96771de.blade.php__components::2cd2bd3a1a36cafc00007578f96771de", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2cd2bd3a1a36cafc00007578f96771de.blade.php&line=1", "ajax": false, "filename": "2cd2bd3a1a36cafc00007578f96771de.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2cd2bd3a1a36cafc00007578f96771de"}, {"name": "1x __components::24c50807435b9d6d46a8d65cd016c8b0", "param_count": null, "params": [], "start": **********.005822, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/24c50807435b9d6d46a8d65cd016c8b0.blade.php__components::24c50807435b9d6d46a8d65cd016c8b0", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F24c50807435b9d6d46a8d65cd016c8b0.blade.php&line=1", "ajax": false, "filename": "24c50807435b9d6d46a8d65cd016c8b0.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::24c50807435b9d6d46a8d65cd016c8b0"}, {"name": "1x __components::13365b7e5a448d13150fdb4b3884b510", "param_count": null, "params": [], "start": **********.008162, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/13365b7e5a448d13150fdb4b3884b510.blade.php__components::13365b7e5a448d13150fdb4b3884b510", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F13365b7e5a448d13150fdb4b3884b510.blade.php&line=1", "ajax": false, "filename": "13365b7e5a448d13150fdb4b3884b510.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::13365b7e5a448d13150fdb4b3884b510"}, {"name": "1x __components::2279ce135fc9e268c1dcef75eaca22a5", "param_count": null, "params": [], "start": **********.011628, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2279ce135fc9e268c1dcef75eaca22a5.blade.php__components::2279ce135fc9e268c1dcef75eaca22a5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2279ce135fc9e268c1dcef75eaca22a5.blade.php&line=1", "ajax": false, "filename": "2279ce135fc9e268c1dcef75eaca22a5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2279ce135fc9e268c1dcef75eaca22a5"}, {"name": "13x __components::d890ecc3acbc4ef41a8ece9e81698457", "param_count": null, "params": [], "start": **********.014299, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/d890ecc3acbc4ef41a8ece9e81698457.blade.php__components::d890ecc3acbc4ef41a8ece9e81698457", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fd890ecc3acbc4ef41a8ece9e81698457.blade.php&line=1", "ajax": false, "filename": "d890ecc3acbc4ef41a8ece9e81698457.blade.php", "line": "?"}, "render_count": 13, "name_original": "__components::d890ecc3acbc4ef41a8ece9e81698457"}, {"name": "1x __components::dd4c2087b0a47210b5b4e3ee87ef3eca", "param_count": null, "params": [], "start": **********.028486, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php__components::dd4c2087b0a47210b5b4e3ee87ef3eca", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdd4c2087b0a47210b5b4e3ee87ef3eca.blade.php&line=1", "ajax": false, "filename": "dd4c2087b0a47210b5b4e3ee87ef3eca.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dd4c2087b0a47210b5b4e3ee87ef3eca"}, {"name": "1x __components::998e4178ecae37dacf7321232f455f64", "param_count": null, "params": [], "start": **********.030989, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/998e4178ecae37dacf7321232f455f64.blade.php__components::998e4178ecae37dacf7321232f455f64", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F998e4178ecae37dacf7321232f455f64.blade.php&line=1", "ajax": false, "filename": "998e4178ecae37dacf7321232f455f64.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::998e4178ecae37dacf7321232f455f64"}, {"name": "1x __components::06e7cfabd119917d6efbd595a344e37b", "param_count": null, "params": [], "start": **********.033091, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/06e7cfabd119917d6efbd595a344e37b.blade.php__components::06e7cfabd119917d6efbd595a344e37b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F06e7cfabd119917d6efbd595a344e37b.blade.php&line=1", "ajax": false, "filename": "06e7cfabd119917d6efbd595a344e37b.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::06e7cfabd119917d6efbd595a344e37b"}, {"name": "3x __components::6e0b6ed9bf49c6ad9d02af2c7f911103", "param_count": null, "params": [], "start": **********.0351, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php__components::6e0b6ed9bf49c6ad9d02af2c7f911103", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php&line=1", "ajax": false, "filename": "6e0b6ed9bf49c6ad9d02af2c7f911103.blade.php", "line": "?"}, "render_count": 3, "name_original": "__components::6e0b6ed9bf49c6ad9d02af2c7f911103"}, {"name": "1x __components::ca20cd1247722214b06db9aa7c493b27", "param_count": null, "params": [], "start": **********.039834, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ca20cd1247722214b06db9aa7c493b27.blade.php__components::ca20cd1247722214b06db9aa7c493b27", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fca20cd1247722214b06db9aa7c493b27.blade.php&line=1", "ajax": false, "filename": "ca20cd1247722214b06db9aa7c493b27.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ca20cd1247722214b06db9aa7c493b27"}, {"name": "1x __components::fd978891e1ac33723cbffddc6658659a", "param_count": null, "params": [], "start": **********.048299, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fd978891e1ac33723cbffddc6658659a.blade.php__components::fd978891e1ac33723cbffddc6658659a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffd978891e1ac33723cbffddc6658659a.blade.php&line=1", "ajax": false, "filename": "fd978891e1ac33723cbffddc6658659a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fd978891e1ac33723cbffddc6658659a"}, {"name": "1x __components::cbce7a4c13e13a70eabb7759894a60fd", "param_count": null, "params": [], "start": **********.055497, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/cbce7a4c13e13a70eabb7759894a60fd.blade.php__components::cbce7a4c13e13a70eabb7759894a60fd", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fcbce7a4c13e13a70eabb7759894a60fd.blade.php&line=1", "ajax": false, "filename": "cbce7a4c13e13a70eabb7759894a60fd.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::cbce7a4c13e13a70eabb7759894a60fd"}, {"name": "1x __components::fe6fcb7551f99a7d9d1c5a4c0011f471", "param_count": null, "params": [], "start": **********.057812, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php__components::fe6fcb7551f99a7d9d1c5a4c0011f471", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffe6fcb7551f99a7d9d1c5a4c0011f471.blade.php&line=1", "ajax": false, "filename": "fe6fcb7551f99a7d9d1c5a4c0011f471.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fe6fcb7551f99a7d9d1c5a4c0011f471"}, {"name": "1x __components::e81d33e262b34e339fefa952b8ffa5f1", "param_count": null, "params": [], "start": **********.060814, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/e81d33e262b34e339fefa952b8ffa5f1.blade.php__components::e81d33e262b34e339fefa952b8ffa5f1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fe81d33e262b34e339fefa952b8ffa5f1.blade.php&line=1", "ajax": false, "filename": "e81d33e262b34e339fefa952b8ffa5f1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e81d33e262b34e339fefa952b8ffa5f1"}, {"name": "1x __components::351bdfbe842eff22e08f1df9d5f5beb1", "param_count": null, "params": [], "start": **********.06453, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/351bdfbe842eff22e08f1df9d5f5beb1.blade.php__components::351bdfbe842eff22e08f1df9d5f5beb1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F351bdfbe842eff22e08f1df9d5f5beb1.blade.php&line=1", "ajax": false, "filename": "351bdfbe842eff22e08f1df9d5f5beb1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::351bdfbe842eff22e08f1df9d5f5beb1"}, {"name": "1x __components::fda51db955ba828a198ee1c8d52b2003", "param_count": null, "params": [], "start": **********.066425, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fda51db955ba828a198ee1c8d52b2003.blade.php__components::fda51db955ba828a198ee1c8d52b2003", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffda51db955ba828a198ee1c8d52b2003.blade.php&line=1", "ajax": false, "filename": "fda51db955ba828a198ee1c8d52b2003.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fda51db955ba828a198ee1c8d52b2003"}, {"name": "1x __components::15422be7d0f2aaf8c8244c1e9db20ad9", "param_count": null, "params": [], "start": **********.076501, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/15422be7d0f2aaf8c8244c1e9db20ad9.blade.php__components::15422be7d0f2aaf8c8244c1e9db20ad9", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F15422be7d0f2aaf8c8244c1e9db20ad9.blade.php&line=1", "ajax": false, "filename": "15422be7d0f2aaf8c8244c1e9db20ad9.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::15422be7d0f2aaf8c8244c1e9db20ad9"}, {"name": "1x __components::2e5e965add6d0ee3aadb02ca38e70825", "param_count": null, "params": [], "start": **********.07901, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2e5e965add6d0ee3aadb02ca38e70825.blade.php__components::2e5e965add6d0ee3aadb02ca38e70825", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2e5e965add6d0ee3aadb02ca38e70825.blade.php&line=1", "ajax": false, "filename": "2e5e965add6d0ee3aadb02ca38e70825.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2e5e965add6d0ee3aadb02ca38e70825"}, {"name": "1x __components::5b8d99843e0f8eff6046d7236026b187", "param_count": null, "params": [], "start": **********.081437, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5b8d99843e0f8eff6046d7236026b187.blade.php__components::5b8d99843e0f8eff6046d7236026b187", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5b8d99843e0f8eff6046d7236026b187.blade.php&line=1", "ajax": false, "filename": "5b8d99843e0f8eff6046d7236026b187.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5b8d99843e0f8eff6046d7236026b187"}, {"name": "2x __components::1d6f928aaf1e585d3246cb3bee8fdd45", "param_count": null, "params": [], "start": **********.083887, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1d6f928aaf1e585d3246cb3bee8fdd45.blade.php__components::1d6f928aaf1e585d3246cb3bee8fdd45", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1d6f928aaf1e585d3246cb3bee8fdd45.blade.php&line=1", "ajax": false, "filename": "1d6f928aaf1e585d3246cb3bee8fdd45.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d6f928aaf1e585d3246cb3bee8fdd45"}, {"name": "1x __components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "param_count": null, "params": [], "start": **********.0863, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Facbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php&line=1", "ajax": false, "filename": "acbc3e3e19c70a7ebb0e7a5d98d84fbe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::acbc3e3e19c70a7ebb0e7a5d98d84fbe"}, {"name": "1x __components::dac323985d9d2618ad252313442aaf03", "param_count": null, "params": [], "start": **********.092742, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/dac323985d9d2618ad252313442aaf03.blade.php__components::dac323985d9d2618ad252313442aaf03", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fdac323985d9d2618ad252313442aaf03.blade.php&line=1", "ajax": false, "filename": "dac323985d9d2618ad252313442aaf03.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::dac323985d9d2618ad252313442aaf03"}, {"name": "1x __components::298cd8a12b86a6f371ff06491a0822fa", "param_count": null, "params": [], "start": **********.094744, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/298cd8a12b86a6f371ff06491a0822fa.blade.php__components::298cd8a12b86a6f371ff06491a0822fa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F298cd8a12b86a6f371ff06491a0822fa.blade.php&line=1", "ajax": false, "filename": "298cd8a12b86a6f371ff06491a0822fa.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::298cd8a12b86a6f371ff06491a0822fa"}, {"name": "1x __components::c59870a61b233f0766e3260625bdb025", "param_count": null, "params": [], "start": **********.09637, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c59870a61b233f0766e3260625bdb025.blade.php__components::c59870a61b233f0766e3260625bdb025", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc59870a61b233f0766e3260625bdb025.blade.php&line=1", "ajax": false, "filename": "c59870a61b233f0766e3260625bdb025.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c59870a61b233f0766e3260625bdb025"}, {"name": "1x __components::3890eca5d46a147ef99ddf994453d2ec", "param_count": null, "params": [], "start": **********.098019, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/3890eca5d46a147ef99ddf994453d2ec.blade.php__components::3890eca5d46a147ef99ddf994453d2ec", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F3890eca5d46a147ef99ddf994453d2ec.blade.php&line=1", "ajax": false, "filename": "3890eca5d46a147ef99ddf994453d2ec.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3890eca5d46a147ef99ddf994453d2ec"}, {"name": "1x __components::ff1fde71531b073b2c658173537aaea5", "param_count": null, "params": [], "start": **********.099616, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ff1fde71531b073b2c658173537aaea5.blade.php__components::ff1fde71531b073b2c658173537aaea5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fff1fde71531b073b2c658173537aaea5.blade.php&line=1", "ajax": false, "filename": "ff1fde71531b073b2c658173537aaea5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff1fde71531b073b2c658173537aaea5"}, {"name": "1x __components::5cef0de51e1489c31c7fcb5d7f2f6a97", "param_count": null, "params": [], "start": **********.101358, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php__components::5cef0de51e1489c31c7fcb5d7f2f6a97", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php&line=1", "ajax": false, "filename": "5cef0de51e1489c31c7fcb5d7f2f6a97.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5cef0de51e1489c31c7fcb5d7f2f6a97"}, {"name": "1x __components::89cb89d3fdb0a0a12f8aa61073c231e6", "param_count": null, "params": [], "start": **********.10297, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/89cb89d3fdb0a0a12f8aa61073c231e6.blade.php__components::89cb89d3fdb0a0a12f8aa61073c231e6", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F89cb89d3fdb0a0a12f8aa61073c231e6.blade.php&line=1", "ajax": false, "filename": "89cb89d3fdb0a0a12f8aa61073c231e6.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::89cb89d3fdb0a0a12f8aa61073c231e6"}, {"name": "1x __components::5a5b09d3f2ee0ddb2b536feb532d230a", "param_count": null, "params": [], "start": **********.104682, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5a5b09d3f2ee0ddb2b536feb532d230a.blade.php__components::5a5b09d3f2ee0ddb2b536feb532d230a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5a5b09d3f2ee0ddb2b536feb532d230a.blade.php&line=1", "ajax": false, "filename": "5a5b09d3f2ee0ddb2b536feb532d230a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5a5b09d3f2ee0ddb2b536feb532d230a"}, {"name": "1x __components::fedc652debeb23dcbb31a98830baa397", "param_count": null, "params": [], "start": **********.10713, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fedc652debeb23dcbb31a98830baa397.blade.php__components::fedc652debeb23dcbb31a98830baa397", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffedc652debeb23dcbb31a98830baa397.blade.php&line=1", "ajax": false, "filename": "fedc652debeb23dcbb31a98830baa397.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fedc652debeb23dcbb31a98830baa397"}, {"name": "1x __components::ff3b2cf4e42e74e63db76ff05c5f2374", "param_count": null, "params": [], "start": **********.110385, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ff3b2cf4e42e74e63db76ff05c5f2374.blade.php__components::ff3b2cf4e42e74e63db76ff05c5f2374", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fff3b2cf4e42e74e63db76ff05c5f2374.blade.php&line=1", "ajax": false, "filename": "ff3b2cf4e42e74e63db76ff05c5f2374.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::ff3b2cf4e42e74e63db76ff05c5f2374"}, {"name": "1x __components::1d5f951b8390a2ab38dcc695dab7b152", "param_count": null, "params": [], "start": **********.112089, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/1d5f951b8390a2ab38dcc695dab7b152.blade.php__components::1d5f951b8390a2ab38dcc695dab7b152", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F1d5f951b8390a2ab38dcc695dab7b152.blade.php&line=1", "ajax": false, "filename": "1d5f951b8390a2ab38dcc695dab7b152.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::1d5f951b8390a2ab38dcc695dab7b152"}, {"name": "1x __components::944e2ad490e8085f4039835ea5eefcbf", "param_count": null, "params": [], "start": **********.113785, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/944e2ad490e8085f4039835ea5eefcbf.blade.php__components::944e2ad490e8085f4039835ea5eefcbf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F944e2ad490e8085f4039835ea5eefcbf.blade.php&line=1", "ajax": false, "filename": "944e2ad490e8085f4039835ea5eefcbf.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::944e2ad490e8085f4039835ea5eefcbf"}, {"name": "1x __components::745871da7c635a3f461dfaeeef54a48e", "param_count": null, "params": [], "start": **********.116058, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/745871da7c635a3f461dfaeeef54a48e.blade.php__components::745871da7c635a3f461dfaeeef54a48e", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F745871da7c635a3f461dfaeeef54a48e.blade.php&line=1", "ajax": false, "filename": "745871da7c635a3f461dfaeeef54a48e.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::745871da7c635a3f461dfaeeef54a48e"}, {"name": "1x __components::2b3233eda7e50501ef45fd875b12da49", "param_count": null, "params": [], "start": **********.120195, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/2b3233eda7e50501ef45fd875b12da49.blade.php__components::2b3233eda7e50501ef45fd875b12da49", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F2b3233eda7e50501ef45fd875b12da49.blade.php&line=1", "ajax": false, "filename": "2b3233eda7e50501ef45fd875b12da49.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::2b3233eda7e50501ef45fd875b12da49"}, {"name": "1x __components::b13663c834a4ae876ef8f72aa0610e8c", "param_count": null, "params": [], "start": **********.122716, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b13663c834a4ae876ef8f72aa0610e8c.blade.php__components::b13663c834a4ae876ef8f72aa0610e8c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb13663c834a4ae876ef8f72aa0610e8c.blade.php&line=1", "ajax": false, "filename": "b13663c834a4ae876ef8f72aa0610e8c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b13663c834a4ae876ef8f72aa0610e8c"}, {"name": "1x core/base::layouts.partials.page-header", "param_count": null, "params": [], "start": **********.123553, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/page-header.blade.phpcore/base::layouts.partials.page-header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Fpage-header.blade.php&line=1", "ajax": false, "filename": "page-header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.page-header"}, {"name": "1x core/base::breadcrumb", "param_count": null, "params": [], "start": **********.124072, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/breadcrumb.blade.phpcore/base::breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::breadcrumb"}, {"name": "1x core/base::layouts.partials.footer", "param_count": null, "params": [], "start": **********.125132, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/partials/footer.blade.phpcore/base::layouts.partials.footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fpartials%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.partials.footer"}, {"name": "1x core/base::partials.copyright", "param_count": null, "params": [], "start": **********.125588, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/partials/copyright.blade.phpcore/base::partials.copyright", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fpartials%2Fcopyright.blade.php&line=1", "ajax": false, "filename": "copyright.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::partials.copyright"}, {"name": "1x core/base::layouts.vertical.partials.after-content", "param_count": null, "params": [], "start": **********.126569, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/layouts/vertical/partials/after-content.blade.phpcore/base::layouts.vertical.partials.after-content", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Flayouts%2Fvertical%2Fpartials%2Fafter-content.blade.php&line=1", "ajax": false, "filename": "after-content.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::layouts.vertical.partials.after-content"}, {"name": "1x core/base::global-search.form", "param_count": null, "params": [], "start": **********.127117, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/global-search/form.blade.phpcore/base::global-search.form", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fglobal-search%2Fform.blade.php&line=1", "ajax": false, "filename": "form.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::global-search.form"}, {"name": "1x __components::3cec1c87224222bda738c53f782c5bc1", "param_count": null, "params": [], "start": **********.128645, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/3cec1c87224222bda738c53f782c5bc1.blade.php__components::3cec1c87224222bda738c53f782c5bc1", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F3cec1c87224222bda738c53f782c5bc1.blade.php&line=1", "ajax": false, "filename": "3cec1c87224222bda738c53f782c5bc1.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::3cec1c87224222bda738c53f782c5bc1"}, {"name": "1x ********************************::form.index", "param_count": null, "params": [], "start": **********.130634, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/form/index.blade.php********************************::form.index", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::form.index"}, {"name": "1x __components::414e4e803eeec6389552bb46515583c5", "param_count": null, "params": [], "start": **********.131527, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/414e4e803eeec6389552bb46515583c5.blade.php__components::414e4e803eeec6389552bb46515583c5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F414e4e803eeec6389552bb46515583c5.blade.php&line=1", "ajax": false, "filename": "414e4e803eeec6389552bb46515583c5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::414e4e803eeec6389552bb46515583c5"}, {"name": "1x __components::c47a448d99d5719cb034f7947c739ff8", "param_count": null, "params": [], "start": **********.132297, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/c47a448d99d5719cb034f7947c739ff8.blade.php__components::c47a448d99d5719cb034f7947c739ff8", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fc47a448d99d5719cb034f7947c739ff8.blade.php&line=1", "ajax": false, "filename": "c47a448d99d5719cb034f7947c739ff8.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::c47a448d99d5719cb034f7947c739ff8"}, {"name": "1x __components::e226165e1fca7eccb10f6857d7cd235a", "param_count": null, "params": [], "start": **********.133052, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/e226165e1fca7eccb10f6857d7cd235a.blade.php__components::e226165e1fca7eccb10f6857d7cd235a", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fe226165e1fca7eccb10f6857d7cd235a.blade.php&line=1", "ajax": false, "filename": "e226165e1fca7eccb10f6857d7cd235a.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::e226165e1fca7eccb10f6857d7cd235a"}, {"name": "1x ********************************::custom-template", "param_count": null, "params": [], "start": **********.134114, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/custom-template.blade.php********************************::custom-template", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fcustom-template.blade.php&line=1", "ajax": false, "filename": "custom-template.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::custom-template"}, {"name": "1x core/media::partials.media", "param_count": null, "params": [], "start": **********.134692, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/media/resources/views/partials/media.blade.phpcore/media::partials.media", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fpartials%2Fmedia.blade.php&line=1", "ajax": false, "filename": "media.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::partials.media"}, {"name": "1x core/media::config", "param_count": null, "params": [], "start": **********.141554, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/media/resources/views/config.blade.phpcore/media::config", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fmedia%2Fresources%2Fviews%2Fconfig.blade.php&line=1", "ajax": false, "filename": "config.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/media::config"}, {"name": "1x ********************************::debug-badge", "param_count": null, "params": [], "start": **********.354073, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/debug-badge.blade.php********************************::debug-badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdebug-badge.blade.php&line=1", "ajax": false, "filename": "debug-badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::debug-badge"}, {"name": "1x __components::fcfb9f9ba5fb460899f38b71f491e1fe", "param_count": null, "params": [], "start": **********.360178, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/fcfb9f9ba5fb460899f38b71f491e1fe.blade.php__components::fcfb9f9ba5fb460899f38b71f491e1fe", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Ffcfb9f9ba5fb460899f38b71f491e1fe.blade.php&line=1", "ajax": false, "filename": "fcfb9f9ba5fb460899f38b71f491e1fe.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fcfb9f9ba5fb460899f38b71f491e1fe"}, {"name": "1x ********************************::layouts.base", "param_count": null, "params": [], "start": **********.36147, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/layouts/base.blade.php********************************::layouts.base", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "********************************::layouts.base"}, {"name": "1x core/base::components.layouts.header", "param_count": null, "params": [], "start": **********.362293, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/layouts/header.blade.phpcore/base::components.layouts.header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Flayouts%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::components.layouts.header"}, {"name": "1x assets::header", "param_count": null, "params": [], "start": **********.364622, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/header.blade.phpassets::header", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::header"}, {"name": "1x core/base::elements.common", "param_count": null, "params": [], "start": **********.366615, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/elements/common.blade.phpcore/base::elements.common", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Felements%2Fcommon.blade.php&line=1", "ajax": false, "filename": "common.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::elements.common"}, {"name": "1x assets::footer", "param_count": null, "params": [], "start": **********.36834, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\vendor\\botble\\assets\\src\\Providers/../../resources/views/footer.blade.phpassets::footer", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fbotble%2Fassets%2Fresources%2Fviews%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}, "render_count": 1, "name_original": "assets::footer"}, {"name": "1x core/base::notification.notification", "param_count": null, "params": [], "start": **********.369308, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/notification/notification.blade.phpcore/base::notification.notification", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fnotification%2Fnotification.blade.php&line=1", "ajax": false, "filename": "notification.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/base::notification.notification"}]}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00861, "accumulated_duration_str": "8.61ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.2548628, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 6.969}, {"sql": "select * from `ec_customers` where `id` = '412' limit 1", "type": "query", "params": [], "bindings": ["412"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 961}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 42}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 209}], "start": **********.25815, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 6.969, "width_percent": 7.549}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.262616, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 14.518, "width_percent": 4.065}, {"sql": "select * from `ec_wish_lists` where `ec_wish_lists`.`customer_id` = 412 and `ec_wish_lists`.`customer_id` is not null", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}, {"index": 22, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Customer.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Customer.php", "line": 140}, {"index": 23, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/CustomerForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\CustomerForm.php", "line": 111}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}], "start": **********.372861, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 18.583, "width_percent": 20.906}, {"sql": "select * from `ec_customer_addresses` where `ec_customer_addresses`.`customer_id` = 412 and `ec_customer_addresses`.`customer_id` is not null", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/CustomerForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\CustomerForm.php", "line": 117}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/CustomerForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\CustomerForm.php", "line": 105}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}], "start": **********.378637, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 39.489, "width_percent": 8.362}, {"sql": "select * from `payments` where `payments`.`customer_id` = 412 and `payments`.`customer_id` is not null", "type": "query", "params": [], "bindings": [412], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/CustomerForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\CustomerForm.php", "line": 135}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Conditionable/Traits/Conditionable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Conditionable\\Traits\\Conditionable.php", "line": 34}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/CustomerForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\CustomerForm.php", "line": 105}, {"index": 20, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}], "start": 1749846439.481526, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 47.851, "width_percent": 10.569}, {"sql": "select exists(select * from `countries`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 561}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/Concerns/HasLocationFields.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\Concerns\\HasLocationFields.php", "line": 29}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/Fronts/Customer/AddressForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\Fronts\\Customer\\AddressForm.php", "line": 58}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/FormFront.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\FormFront.php", "line": 48}], "start": **********.052266, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:561", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 561}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=561", "ajax": false, "filename": "EcommerceHelper.php", "line": "561"}, "connection": "muhrak", "explain": null, "start_percent": 58.42, "width_percent": 17.189}, {"sql": "select exists(select * from `states`) as `exists`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 562}, {"index": 13, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/Concerns/HasLocationFields.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\Concerns\\HasLocationFields.php", "line": 29}, {"index": 14, "namespace": null, "name": "platform/plugins/ecommerce/src/Forms/Fronts/Customer/AddressForm.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Forms\\Fronts\\Customer\\AddressForm.php", "line": 58}, {"index": 15, "namespace": null, "name": "platform/core/base/src/Forms/FormAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Forms\\FormAbstract.php", "line": 100}, {"index": 16, "namespace": null, "name": "platform/packages/theme/src/FormFront.php", "file": "D:\\laragon\\www\\muhrak\\platform\\packages\\theme\\src\\FormFront.php", "line": 48}], "start": **********.055494, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "EcommerceHelper.php:562", "source": {"index": 11, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/EcommerceHelper.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\EcommerceHelper.php", "line": 562}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FSupports%2FEcommerceHelper.php&line=562", "ajax": false, "filename": "EcommerceHelper.php", "line": "562"}, "connection": "muhrak", "explain": null, "start_percent": 75.61, "width_percent": 13.357}, {"sql": "select count(*) as aggregate from `ec_orders` where (`status` = 'pending' and `is_finished` = 1)", "type": "query", "params": [], "bindings": ["pending", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1249}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9220521, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1249", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1249}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1249", "ajax": false, "filename": "HookServiceProvider.php", "line": "1249"}, "connection": "muhrak", "explain": null, "start_percent": 88.966, "width_percent": 5.343}, {"sql": "select count(*) as aggregate from `ec_reviews` where `status` = 'pending'", "type": "query", "params": [], "bindings": ["pending"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1293}, {"index": 20, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.000497, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "HookServiceProvider.php:1293", "source": {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Providers\\HookServiceProvider.php", "line": 1293}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FProviders%2FHookServiceProvider.php&line=1293", "ajax": false, "filename": "HookServiceProvider.php", "line": "1293"}, "connection": "muhrak", "explain": null, "start_percent": 94.309, "width_percent": 5.691}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}}, "count": 4, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/customers/edit/412", "action_name": "customers.edit", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Customers\\CustomerController@edit", "uri": "GET admin/customers/edit/{customer}", "controller": "Botble\\Ecommerce\\Http\\Controllers\\Customers\\CustomerController@edit<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FCustomers%2FCustomerController.php&line=69\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers\\Customers", "prefix": "admin/customers", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FCustomers%2FCustomerController.php&line=69\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/Customers/CustomerController.php:69-78</a>", "middleware": "web, core, auth", "duration": "8.87s", "peak_memory": "70MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1488864220 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1488864220\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1472907149 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1472907149\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1845466683 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">https://muhrak.gc/admin/marketplaces/vendors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNLbDVUbjBhdGR1cFdXUnNxbGZJZWc9PSIsInZhbHVlIjoiaFVkM2d5RUNYNnFjR294WUJ1WTZ1S205Q0xNUE5OY3BGUStQakpiMnF5dUlwMEVIalUvVWNxYWxtWHNrRDhRWXN1STQxb3loZTdXOUNBdmo5UDZNVTg5b0FOY0RFSVJSSVRySjFxaGdkMmhsN1dtTWlsWEVBM09EZDUxaFIxQ3ciLCJtYWMiOiI4MzVkYWRmNmI3NzkyNjkxZjVjMjhjZmEzNjc4Njk1NmFhNmIwMTBkODQ0NjMxZGJkNjZmZjMzMjIyMjQzNDA3IiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IjRCSjdHbDFES2F5eDZKUThBWGJwTVE9PSIsInZhbHVlIjoiaHpjSzI1OXlzUm1DMmZVTGs2Q2dDSEpMWGdXNXQ0RTFtcmROdjY2TFhldmVyc0ZXT3h4U2dYKytFbnVrQU1vUzkwam5aWE9DRnV1S3h3TTFVekVRVHJmNGh0WnhmNFhsWWVsQ25URjU3SEltWStFT2VwZ2t3VDdFdWlJekVJL2siLCJtYWMiOiI4ODJkMTI4NzI1ZTZmNWRjOGEyN2Y3N2M4YjMzODVjYzQyZjU4MWRkNmNmMjQ0YzAyNWQ2MmYxYjZjNzQ1MjA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1845466683\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-213419903 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-213419903\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-151194331 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 20:27:24 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-151194331\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1563990912 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"44 characters\">https://muhrak.gc/admin/marketplaces/vendors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1563990912\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/customers/edit/412", "action_name": "customers.edit", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\Customers\\CustomerController@edit"}, "badge": null}}