<?php

namespace Botble\Marketplace\Commands;

use Botble\Ecommerce\Models\Customer;
use Botble\Marketplace\Models\Store;
use Botble\Marketplace\Models\VendorInfo;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\Console\Attribute\AsCommand;

#[AsCommand('marketplace:create-vendor-accounts', 'Create individual vendor accounts for all stores')]
class CreateVendorAccountsCommand extends Command
{
    protected $signature = 'marketplace:create-vendor-accounts 
                            {--dry-run : Show what would be done without making changes}
                            {--password=******** : Default password for vendor accounts}';

    protected $description = 'Create individual vendor accounts for each store and link them properly';

    public function handle(): int
    {
        $isDryRun = $this->option('dry-run');
        $defaultPassword = $this->option('password');

        $this->info('Starting vendor account creation process...');
        
        if ($isDryRun) {
            $this->warn('DRY RUN MODE - No changes will be made');
        }

        // Get all stores
        $stores = Store::all();
        $totalStores = $stores->count();

        $this->info("Found {$totalStores} stores to process");

        if ($totalStores === 0) {
            $this->error('No stores found in the system');
            return self::FAILURE;
        }

        // Check current customer assignments
        $customerAssignments = Store::selectRaw('customer_id, count(*) as store_count')
            ->groupBy('customer_id')
            ->get();

        $this->info('Current store assignments:');
        foreach ($customerAssignments as $assignment) {
            $this->line("  Customer ID {$assignment->customer_id}: {$assignment->store_count} stores");
        }

        if (!$this->confirm('Do you want to proceed with creating vendor accounts?')) {
            $this->info('Operation cancelled');
            return self::SUCCESS;
        }

        $progressBar = $this->output->createProgressBar($totalStores);
        $progressBar->start();

        $createdVendors = 0;
        $errors = [];

        DB::beginTransaction();

        try {
            foreach ($stores as $store) {
                try {
                    if (!$isDryRun) {
                        $vendor = $this->createVendorAccount($store, $defaultPassword);
                        $this->createVendorInfo($vendor);
                        $this->linkStoreToVendor($store, $vendor);
                    }
                    
                    $createdVendors++;
                    $progressBar->advance();
                } catch (\Exception $e) {
                    $errors[] = "Store ID {$store->id}: " . $e->getMessage();
                    $progressBar->advance();
                }
            }

            $progressBar->finish();
            $this->newLine(2);

            if (empty($errors)) {
                if (!$isDryRun) {
                    DB::commit();
                    $this->info("✅ Successfully created {$createdVendors} vendor accounts");
                } else {
                    DB::rollBack();
                    $this->info("✅ Dry run completed - would create {$createdVendors} vendor accounts");
                }
            } else {
                DB::rollBack();
                $this->error("❌ Operation failed with " . count($errors) . " errors:");
                foreach ($errors as $error) {
                    $this->error("  - {$error}");
                }
                return self::FAILURE;
            }

        } catch (\Exception $e) {
            DB::rollBack();
            $this->error("❌ Transaction failed: " . $e->getMessage());
            return self::FAILURE;
        }

        // Show final statistics
        if (!$isDryRun) {
            $this->showFinalStatistics();
        }

        return self::SUCCESS;
    }

    private function createVendorAccount(Store $store, string $password): Customer
    {
        $vendor = new Customer();
        $vendor->name = $store->name . ' Vendor';
        $vendor->email = "vendor{$store->id}@example.com";
        $vendor->password = Hash::make($password);
        $vendor->is_vendor = true;
        $vendor->vendor_verified_at = Carbon::now();
        $vendor->confirmed_at = Carbon::now();
        $vendor->status = 'activated';
        $vendor->save();

        return $vendor;
    }

    private function createVendorInfo(Customer $vendor): VendorInfo
    {
        $vendorInfo = new VendorInfo();
        $vendorInfo->customer_id = $vendor->id;
        $vendorInfo->balance = 0;
        $vendorInfo->total_fee = 0;
        $vendorInfo->total_revenue = 0;
        $vendorInfo->bank_info = [
            'name' => $vendor->name,
            'number' => '',
            'full_name' => $vendor->name,
            'description' => 'Auto-generated vendor account',
        ];
        $vendorInfo->save();

        return $vendorInfo;
    }

    private function linkStoreToVendor(Store $store, Customer $vendor): void
    {
        $store->customer_id = $vendor->id;
        $store->save();
    }

    private function showFinalStatistics(): void
    {
        $this->newLine();
        $this->info('=== Final Statistics ===');
        
        $totalCustomers = Customer::count();
        $totalVendors = Customer::where('is_vendor', true)->count();
        $totalStores = Store::count();
        
        $this->info("Total customers: {$totalCustomers}");
        $this->info("Total vendors: {$totalVendors}");
        $this->info("Total stores: {$totalStores}");

        // Show new store assignments
        $newAssignments = Store::selectRaw('customer_id, count(*) as store_count')
            ->groupBy('customer_id')
            ->orderBy('store_count', 'desc')
            ->get();

        $this->info('New store assignments:');
        foreach ($newAssignments->take(5) as $assignment) {
            $this->line("  Customer ID {$assignment->customer_id}: {$assignment->store_count} stores");
        }
        
        if ($newAssignments->count() > 5) {
            $this->line("  ... and " . ($newAssignments->count() - 5) . " more");
        }
    }
}
