<?php

namespace App\Http\Controllers;

use Botble\Ecommerce\Enums\MainTypeEnum;
use Botble\Ecommerce\Models\Product;
use Botble\Marketplace\Models\Store;

class TempController extends Controller
{
    public function importProductsFromCsv()
    {
        // CSV file ka path
        $filePath = storage_path('app/products_export.csv');

        if (!file_exists($filePath)) {
            return 'CSV file not found!';
        }

        // Native PHP CSV reader use karte hain
        $handle = fopen($filePath, 'r');

        // Pehla row skip karo (header)
        $header = fgetcsv($handle);

        $batchSize = 500;
        $batch = [];

        while (($row = fgetcsv($handle)) !== false) {
            $batch[] = $row;

            if (count($batch) >= $batchSize) {
                $this->processCsvBatch($batch);
                $batch = [];
            }
        }

        // Last remaining rows process karo
        if (!empty($batch)) {
            $this->processCsvBatch($batch);
        }

        fclose($handle);

        return 'Products imported and updated successfully!';
    }

    private function processCsvBatch(array $rows)
    {
        foreach ($rows as $row) {
            // CSV Columns: [0] => Name, [1] => Brand, [2] => Product Main Type
            $productName = $row[0];
            $brandName = $row[1];
            $mainTypeValue = $row[2];

            // 1) Product get karo
            $product = Product::where('name', $productName)->first();
            if (!$product) {
                continue; // Not found, skip
            }

            // 2) Brand name se Store find karo
            $store = Store::where('name', $brandName)->first();
            if ($store) {
                $product->store_id = $store->id; // Update store_id
            }

            // 3) Main type ko convert karo
            $mainType = null;
            if ($mainTypeValue == 0) {
                $mainType = MainTypeEnum::INDUSTRIAL;
            } elseif ($mainTypeValue == 1) {
                $mainType =  MainTypeEnum::HEAVY_EQUIPMENT;
            } elseif ($mainTypeValue == 2) {
                $mainType = MainTypeEnum::MARINE;
            }

            $product->main_type = $mainType;

            // 4) Save karo
            $product->save();
        }
    }
}
