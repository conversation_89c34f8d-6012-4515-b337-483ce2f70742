{"__meta": {"id": "01JXNHDQDYZ8XSEJ429KGK9BY6", "datetime": "2025-06-13 20:59:10", "utime": **********.143487, "method": "POST", "uri": "/admin/marketplaces/stores?", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1749848347.979077, "end": **********.143511, "duration": 2.1644339561462402, "duration_str": "2.16s", "measures": [{"label": "Booting", "start": 1749848347.979077, "relative_start": 0, "end": **********.75518, "relative_end": **********.75518, "duration": 0.****************, "duration_str": "776ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.75519, "relative_start": 0.****************, "end": **********.143514, "relative_end": 2.86102294921875e-06, "duration": 1.****************, "duration_str": "1.39s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.774772, "relative_start": 0.****************, "end": **********.785584, "relative_end": **********.785584, "duration": 0.010812044143676758, "duration_str": "10.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.84423, "relative_start": 0.****************, "end": **********.84423, "relative_end": **********.84423, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.847979, "relative_start": 0.****************, "end": **********.847979, "relative_end": **********.847979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3952f2f8079cae92614235bd0a03d56", "start": **********.851217, "relative_start": 0.8721399307250977, "end": **********.851217, "relative_end": **********.851217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.851586, "relative_start": 0.8725090026855469, "end": **********.851586, "relative_end": **********.851586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.852136, "relative_start": 0.8730587959289551, "end": **********.852136, "relative_end": **********.852136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.852597, "relative_start": 0.8735198974609375, "end": **********.852597, "relative_end": **********.852597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.862472, "relative_start": 0.8833949565887451, "end": **********.862472, "relative_end": **********.862472, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::21a9e4f6a6bd57aad5711446b399a39c", "start": **********.863925, "relative_start": 0.88484787940979, "end": **********.863925, "relative_end": **********.863925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::includes.header-action", "start": **********.865605, "relative_start": 0.8865280151367188, "end": **********.865605, "relative_end": **********.865605, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::60cb3c84e43d8fd13d066b6332a243d5", "start": **********.866665, "relative_start": 0.8875877857208252, "end": **********.866665, "relative_end": **********.866665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::includes.header-action", "start": **********.867049, "relative_start": 0.8879718780517578, "end": **********.867049, "relative_end": **********.867049, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5d51c9333cac7b48f699a15fa5950d60", "start": **********.867972, "relative_start": 0.88889479637146, "end": **********.867972, "relative_end": **********.867972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.869808, "relative_start": 0.8907308578491211, "end": **********.869808, "relative_end": **********.869808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.870814, "relative_start": 0.8917369842529297, "end": **********.870814, "relative_end": **********.870814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::badge", "start": **********.871268, "relative_start": 0.8921909332275391, "end": **********.871268, "relative_end": **********.871268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.141527, "relative_start": 1.162449836730957, "end": 1749848349.141527, "relative_end": 1749848349.141527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.142298, "relative_start": 1.1632208824157715, "end": 1749848349.142298, "relative_end": 1749848349.142298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.142804, "relative_start": 1.163726806640625, "end": 1749848349.142804, "relative_end": 1749848349.142804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.143608, "relative_start": 1.1645309925079346, "end": 1749848349.143608, "relative_end": 1749848349.143608, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.144627, "relative_start": 1.1655499935150146, "end": 1749848349.144627, "relative_end": 1749848349.144627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.144914, "relative_start": 1.1658368110656738, "end": 1749848349.144914, "relative_end": 1749848349.144914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.145325, "relative_start": 1.166247844696045, "end": 1749848349.145325, "relative_end": 1749848349.145325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.146042, "relative_start": 1.1669650077819824, "end": 1749848349.146042, "relative_end": 1749848349.146042, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.14749, "relative_start": 1.1684129238128662, "end": 1749848349.14749, "relative_end": 1749848349.14749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.148003, "relative_start": 1.1689260005950928, "end": 1749848349.148003, "relative_end": 1749848349.148003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.148403, "relative_start": 1.169325828552246, "end": 1749848349.148403, "relative_end": 1749848349.148403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.14932, "relative_start": 1.1702427864074707, "end": 1749848349.14932, "relative_end": 1749848349.14932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.150749, "relative_start": 1.1716718673706055, "end": 1749848349.150749, "relative_end": 1749848349.150749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.151547, "relative_start": 1.1724698543548584, "end": 1749848349.151547, "relative_end": 1749848349.151547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.154526, "relative_start": 1.1754488945007324, "end": 1749848349.154526, "relative_end": 1749848349.154526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.156898, "relative_start": 1.1778209209442139, "end": 1749848349.156898, "relative_end": 1749848349.156898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.16308, "relative_start": 1.1840028762817383, "end": 1749848349.16308, "relative_end": 1749848349.16308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.191325, "relative_start": 1.2122478485107422, "end": 1749848349.191325, "relative_end": 1749848349.191325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.191876, "relative_start": 1.212798833847046, "end": 1749848349.191876, "relative_end": 1749848349.191876, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.192143, "relative_start": 1.2130658626556396, "end": 1749848349.192143, "relative_end": 1749848349.192143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.192641, "relative_start": 1.2135639190673828, "end": 1749848349.192641, "relative_end": 1749848349.192641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.19326, "relative_start": 1.2141828536987305, "end": 1749848349.19326, "relative_end": 1749848349.19326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.193501, "relative_start": 1.2144238948822021, "end": 1749848349.193501, "relative_end": 1749848349.193501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.193745, "relative_start": 1.214667797088623, "end": 1749848349.193745, "relative_end": 1749848349.193745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.194195, "relative_start": 1.2151179313659668, "end": 1749848349.194195, "relative_end": 1749848349.194195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.194846, "relative_start": 1.215768814086914, "end": 1749848349.194846, "relative_end": 1749848349.194846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.195188, "relative_start": 1.2161109447479248, "end": 1749848349.195188, "relative_end": 1749848349.195188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.19552, "relative_start": 1.2164428234100342, "end": 1749848349.19552, "relative_end": 1749848349.19552, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.196303, "relative_start": 1.2172257900238037, "end": 1749848349.196303, "relative_end": 1749848349.196303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.196952, "relative_start": 1.2178750038146973, "end": 1749848349.196952, "relative_end": 1749848349.196952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.197329, "relative_start": 1.2182519435882568, "end": 1749848349.197329, "relative_end": 1749848349.197329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.201247, "relative_start": 1.2221698760986328, "end": 1749848349.201247, "relative_end": 1749848349.201247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.203697, "relative_start": 1.2246198654174805, "end": 1749848349.203697, "relative_end": 1749848349.203697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.205133, "relative_start": 1.2260558605194092, "end": 1749848349.205133, "relative_end": 1749848349.205133, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.213953, "relative_start": 1.2348759174346924, "end": 1749848349.213953, "relative_end": 1749848349.213953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.214562, "relative_start": 1.2354848384857178, "end": 1749848349.214562, "relative_end": 1749848349.214562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.21484, "relative_start": 1.2357628345489502, "end": 1749848349.21484, "relative_end": 1749848349.21484, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.215363, "relative_start": 1.236285924911499, "end": 1749848349.215363, "relative_end": 1749848349.215363, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.215981, "relative_start": 1.2369039058685303, "end": 1749848349.215981, "relative_end": 1749848349.215981, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.216249, "relative_start": 1.2371718883514404, "end": 1749848349.216249, "relative_end": 1749848349.216249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.216497, "relative_start": 1.237419843673706, "end": 1749848349.216497, "relative_end": 1749848349.216497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.217097, "relative_start": 1.2380199432373047, "end": 1749848349.217097, "relative_end": 1749848349.217097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.217569, "relative_start": 1.2384920120239258, "end": 1749848349.217569, "relative_end": 1749848349.217569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.217933, "relative_start": 1.2388558387756348, "end": 1749848349.217933, "relative_end": 1749848349.217933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.218289, "relative_start": 1.2392117977142334, "end": 1749848349.218289, "relative_end": 1749848349.218289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.219015, "relative_start": 1.2399377822875977, "end": 1749848349.219015, "relative_end": 1749848349.219015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.219611, "relative_start": 1.2405338287353516, "end": 1749848349.219611, "relative_end": 1749848349.219611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.220001, "relative_start": 1.2409238815307617, "end": 1749848349.220001, "relative_end": 1749848349.220001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.223329, "relative_start": 1.2442519664764404, "end": 1749848349.223329, "relative_end": 1749848349.223329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.225573, "relative_start": 1.2464959621429443, "end": 1749848349.225573, "relative_end": 1749848349.225573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.227013, "relative_start": 1.2479360103607178, "end": 1749848349.227013, "relative_end": 1749848349.227013, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.235359, "relative_start": 1.256281852722168, "end": 1749848349.235359, "relative_end": 1749848349.235359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.235956, "relative_start": 1.2568788528442383, "end": 1749848349.235956, "relative_end": 1749848349.235956, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.236233, "relative_start": 1.2571558952331543, "end": 1749848349.236233, "relative_end": 1749848349.236233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.236716, "relative_start": 1.257638931274414, "end": 1749848349.236716, "relative_end": 1749848349.236716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.237185, "relative_start": 1.2581079006195068, "end": 1749848349.237185, "relative_end": 1749848349.237185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.237424, "relative_start": 1.2583467960357666, "end": 1749848349.237424, "relative_end": 1749848349.237424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.237668, "relative_start": 1.2585909366607666, "end": 1749848349.237668, "relative_end": 1749848349.237668, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.238127, "relative_start": 1.259049892425537, "end": 1749848349.238127, "relative_end": 1749848349.238127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.238535, "relative_start": 1.2594578266143799, "end": 1749848349.238535, "relative_end": 1749848349.238535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.238773, "relative_start": 1.2596960067749023, "end": 1749848349.238773, "relative_end": 1749848349.238773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.239021, "relative_start": 1.259943962097168, "end": 1749848349.239021, "relative_end": 1749848349.239021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.239492, "relative_start": 1.2604148387908936, "end": 1749848349.239492, "relative_end": 1749848349.239492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.239901, "relative_start": 1.2608239650726318, "end": 1749848349.239901, "relative_end": 1749848349.239901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.240183, "relative_start": 1.261106014251709, "end": 1749848349.240183, "relative_end": 1749848349.240183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.242124, "relative_start": 1.2630469799041748, "end": 1749848349.242124, "relative_end": 1749848349.242124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.244139, "relative_start": 1.265061855316162, "end": 1749848349.244139, "relative_end": 1749848349.244139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.245774, "relative_start": 1.2666969299316406, "end": 1749848349.245774, "relative_end": 1749848349.245774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.25416, "relative_start": 1.2750828266143799, "end": 1749848349.25416, "relative_end": 1749848349.25416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.254762, "relative_start": 1.2756848335266113, "end": 1749848349.254762, "relative_end": 1749848349.254762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.255057, "relative_start": 1.275979995727539, "end": 1749848349.255057, "relative_end": 1749848349.255057, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.255649, "relative_start": 1.2765719890594482, "end": 1749848349.255649, "relative_end": 1749848349.255649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.256141, "relative_start": 1.2770638465881348, "end": 1749848349.256141, "relative_end": 1749848349.256141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.256375, "relative_start": 1.2772979736328125, "end": 1749848349.256375, "relative_end": 1749848349.256375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.25661, "relative_start": 1.2775328159332275, "end": 1749848349.25661, "relative_end": 1749848349.25661, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.257095, "relative_start": 1.2780179977416992, "end": 1749848349.257095, "relative_end": 1749848349.257095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.257574, "relative_start": 1.2784969806671143, "end": 1749848349.257574, "relative_end": 1749848349.257574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.257856, "relative_start": 1.2787787914276123, "end": 1749848349.257856, "relative_end": 1749848349.257856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.258139, "relative_start": 1.2790617942810059, "end": 1749848349.258139, "relative_end": 1749848349.258139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.25863, "relative_start": 1.279552936553955, "end": 1749848349.25863, "relative_end": 1749848349.25863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.259063, "relative_start": 1.2799859046936035, "end": 1749848349.259063, "relative_end": 1749848349.259063, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.259367, "relative_start": 1.280289888381958, "end": 1749848349.259367, "relative_end": 1749848349.259367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.261396, "relative_start": 1.2823188304901123, "end": 1749848349.261396, "relative_end": 1749848349.261396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.264465, "relative_start": 1.2853879928588867, "end": 1749848349.264465, "relative_end": 1749848349.264465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.265938, "relative_start": 1.2868609428405762, "end": 1749848349.265938, "relative_end": 1749848349.265938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.273424, "relative_start": 1.294346809387207, "end": 1749848349.273424, "relative_end": 1749848349.273424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.273991, "relative_start": 1.2949140071868896, "end": 1749848349.273991, "relative_end": 1749848349.273991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.274259, "relative_start": 1.2951819896697998, "end": 1749848349.274259, "relative_end": 1749848349.274259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.274737, "relative_start": 1.2956597805023193, "end": 1749848349.274737, "relative_end": 1749848349.274737, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.275198, "relative_start": 1.2961208820343018, "end": 1749848349.275198, "relative_end": 1749848349.275198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.275437, "relative_start": 1.2963600158691406, "end": 1749848349.275437, "relative_end": 1749848349.275437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.27568, "relative_start": 1.2966029644012451, "end": 1749848349.27568, "relative_end": 1749848349.27568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.276238, "relative_start": 1.2971608638763428, "end": 1749848349.276238, "relative_end": 1749848349.276238, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.276656, "relative_start": 1.2975788116455078, "end": 1749848349.276656, "relative_end": 1749848349.276656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.276879, "relative_start": 1.2978019714355469, "end": 1749848349.276879, "relative_end": 1749848349.276879, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.27726, "relative_start": 1.2981829643249512, "end": 1749848349.27726, "relative_end": 1749848349.27726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.277974, "relative_start": 1.2988967895507812, "end": 1749848349.277974, "relative_end": 1749848349.277974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.278757, "relative_start": 1.2996799945831299, "end": 1749848349.278757, "relative_end": 1749848349.278757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.279132, "relative_start": 1.3000547885894775, "end": 1749848349.279132, "relative_end": 1749848349.279132, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.281686, "relative_start": 1.3026089668273926, "end": 1749848349.281686, "relative_end": 1749848349.281686, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.28406, "relative_start": 1.3049829006195068, "end": 1749848349.28406, "relative_end": 1749848349.28406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.285625, "relative_start": 1.3065478801727295, "end": 1749848349.285625, "relative_end": 1749848349.285625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.293625, "relative_start": 1.3145480155944824, "end": 1749848349.293625, "relative_end": 1749848349.293625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.294306, "relative_start": 1.3152289390563965, "end": 1749848349.294306, "relative_end": 1749848349.294306, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.294708, "relative_start": 1.3156309127807617, "end": 1749848349.294708, "relative_end": 1749848349.294708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.295369, "relative_start": 1.3162918090820312, "end": 1749848349.295369, "relative_end": 1749848349.295369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.296225, "relative_start": 1.317147970199585, "end": 1749848349.296225, "relative_end": 1749848349.296225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.29672, "relative_start": 1.3176429271697998, "end": 1749848349.29672, "relative_end": 1749848349.29672, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.297095, "relative_start": 1.3180179595947266, "end": 1749848349.297095, "relative_end": 1749848349.297095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.297709, "relative_start": 1.318631887435913, "end": 1749848349.297709, "relative_end": 1749848349.297709, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.29821, "relative_start": 1.3191328048706055, "end": 1749848349.29821, "relative_end": 1749848349.29821, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.298473, "relative_start": 1.3193957805633545, "end": 1749848349.298473, "relative_end": 1749848349.298473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.29875, "relative_start": 1.3196728229522705, "end": 1749848349.29875, "relative_end": 1749848349.29875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.29932, "relative_start": 1.3202428817749023, "end": 1749848349.29932, "relative_end": 1749848349.29932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.299815, "relative_start": 1.3207378387451172, "end": 1749848349.299815, "relative_end": 1749848349.299815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.300118, "relative_start": 1.3210408687591553, "end": 1749848349.300118, "relative_end": 1749848349.300118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.302646, "relative_start": 1.3235688209533691, "end": 1749848349.302646, "relative_end": 1749848349.302646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.305192, "relative_start": 1.3261148929595947, "end": 1749848349.305192, "relative_end": 1749848349.305192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.306971, "relative_start": 1.3278939723968506, "end": 1749848349.306971, "relative_end": 1749848349.306971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.318583, "relative_start": 1.339505910873413, "end": 1749848349.318583, "relative_end": 1749848349.318583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.319181, "relative_start": 1.3401038646697998, "end": 1749848349.319181, "relative_end": 1749848349.319181, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.319453, "relative_start": 1.3403759002685547, "end": 1749848349.319453, "relative_end": 1749848349.319453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.319944, "relative_start": 1.3408668041229248, "end": 1749848349.319944, "relative_end": 1749848349.319944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.32044, "relative_start": 1.3413629531860352, "end": 1749848349.32044, "relative_end": 1749848349.32044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.320712, "relative_start": 1.34163498878479, "end": 1749848349.320712, "relative_end": 1749848349.320712, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.320969, "relative_start": 1.3418920040130615, "end": 1749848349.320969, "relative_end": 1749848349.320969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.321425, "relative_start": 1.3423478603363037, "end": 1749848349.321425, "relative_end": 1749848349.321425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.321836, "relative_start": 1.3427588939666748, "end": 1749848349.321836, "relative_end": 1749848349.321836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.322064, "relative_start": 1.342986822128296, "end": 1749848349.322064, "relative_end": 1749848349.322064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.322292, "relative_start": 1.343214988708496, "end": 1749848349.322292, "relative_end": 1749848349.322292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.322757, "relative_start": 1.3436799049377441, "end": 1749848349.322757, "relative_end": 1749848349.322757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.323164, "relative_start": 1.3440868854522705, "end": 1749848349.323164, "relative_end": 1749848349.323164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.323435, "relative_start": 1.344357967376709, "end": 1749848349.323435, "relative_end": 1749848349.323435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.32557, "relative_start": 1.3464930057525635, "end": 1749848349.32557, "relative_end": 1749848349.32557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.32873, "relative_start": 1.3496530055999756, "end": 1749848349.32873, "relative_end": 1749848349.32873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.331326, "relative_start": 1.3522489070892334, "end": 1749848349.331326, "relative_end": 1749848349.331326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.340444, "relative_start": 1.3613669872283936, "end": 1749848349.340444, "relative_end": 1749848349.340444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.341079, "relative_start": 1.362001895904541, "end": 1749848349.341079, "relative_end": 1749848349.341079, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.341377, "relative_start": 1.362299919128418, "end": 1749848349.341377, "relative_end": 1749848349.341377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.341925, "relative_start": 1.3628478050231934, "end": 1749848349.341925, "relative_end": 1749848349.341925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.342437, "relative_start": 1.3633599281311035, "end": 1749848349.342437, "relative_end": 1749848349.342437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.342708, "relative_start": 1.363631010055542, "end": 1749848349.342708, "relative_end": 1749848349.342708, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.343064, "relative_start": 1.3639869689941406, "end": 1749848349.343064, "relative_end": 1749848349.343064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.343573, "relative_start": 1.3644959926605225, "end": 1749848349.343573, "relative_end": 1749848349.343573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.344039, "relative_start": 1.364961862564087, "end": 1749848349.344039, "relative_end": 1749848349.344039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.344308, "relative_start": 1.3652307987213135, "end": 1749848349.344308, "relative_end": 1749848349.344308, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.344555, "relative_start": 1.3654778003692627, "end": 1749848349.344555, "relative_end": 1749848349.344555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.345075, "relative_start": 1.3659977912902832, "end": 1749848349.345075, "relative_end": 1749848349.345075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.345488, "relative_start": 1.3664109706878662, "end": 1749848349.345488, "relative_end": 1749848349.345488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.345776, "relative_start": 1.366698980331421, "end": 1749848349.345776, "relative_end": 1749848349.345776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.349034, "relative_start": 1.3699569702148438, "end": 1749848349.349034, "relative_end": 1749848349.349034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.352828, "relative_start": 1.373750925064087, "end": 1749848349.352828, "relative_end": 1749848349.352828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.355319, "relative_start": 1.37624192237854, "end": 1749848349.355319, "relative_end": 1749848349.355319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.366877, "relative_start": 1.3877999782562256, "end": 1749848349.366877, "relative_end": 1749848349.366877, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.367735, "relative_start": 1.388657808303833, "end": 1749848349.367735, "relative_end": 1749848349.367735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.367994, "relative_start": 1.3889169692993164, "end": 1749848349.367994, "relative_end": 1749848349.367994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.368474, "relative_start": 1.3893969058990479, "end": 1749848349.368474, "relative_end": 1749848349.368474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.368943, "relative_start": 1.3898658752441406, "end": 1749848349.368943, "relative_end": 1749848349.368943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.369171, "relative_start": 1.3900938034057617, "end": 1749848349.369171, "relative_end": 1749848349.369171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.3694, "relative_start": 1.3903229236602783, "end": 1749848349.3694, "relative_end": 1749848349.3694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.369848, "relative_start": 1.3907709121704102, "end": 1749848349.369848, "relative_end": 1749848349.369848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.370254, "relative_start": 1.3911769390106201, "end": 1749848349.370254, "relative_end": 1749848349.370254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.37048, "relative_start": 1.3914029598236084, "end": 1749848349.37048, "relative_end": 1749848349.37048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.370714, "relative_start": 1.391636848449707, "end": 1749848349.370714, "relative_end": 1749848349.370714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.37118, "relative_start": 1.3921029567718506, "end": 1749848349.37118, "relative_end": 1749848349.37118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.371581, "relative_start": 1.3925039768218994, "end": 1749848349.371581, "relative_end": 1749848349.371581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.371853, "relative_start": 1.3927760124206543, "end": 1749848349.371853, "relative_end": 1749848349.371853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.373835, "relative_start": 1.3947579860687256, "end": 1749848349.373835, "relative_end": 1749848349.373835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.375784, "relative_start": 1.3967068195343018, "end": 1749848349.375784, "relative_end": 1749848349.375784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.377287, "relative_start": 1.398209810256958, "end": 1749848349.377287, "relative_end": 1749848349.377287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.385757, "relative_start": 1.4066798686981201, "end": 1749848349.385757, "relative_end": 1749848349.385757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.386378, "relative_start": 1.4073009490966797, "end": 1749848349.386378, "relative_end": 1749848349.386378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.386649, "relative_start": 1.407571792602539, "end": 1749848349.386649, "relative_end": 1749848349.386649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.387129, "relative_start": 1.4080519676208496, "end": 1749848349.387129, "relative_end": 1749848349.387129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.387596, "relative_start": 1.4085187911987305, "end": 1749848349.387596, "relative_end": 1749848349.387596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.387837, "relative_start": 1.4087598323822021, "end": 1749848349.387837, "relative_end": 1749848349.387837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.388101, "relative_start": 1.4090240001678467, "end": 1749848349.388101, "relative_end": 1749848349.388101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.388637, "relative_start": 1.409559965133667, "end": 1749848349.388637, "relative_end": 1749848349.388637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.389052, "relative_start": 1.4099748134613037, "end": 1749848349.389052, "relative_end": 1749848349.389052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.3893, "relative_start": 1.4102230072021484, "end": 1749848349.3893, "relative_end": 1749848349.3893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.389551, "relative_start": 1.4104738235473633, "end": 1749848349.389551, "relative_end": 1749848349.389551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.390015, "relative_start": 1.410937786102295, "end": 1749848349.390015, "relative_end": 1749848349.390015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.390424, "relative_start": 1.4113469123840332, "end": 1749848349.390424, "relative_end": 1749848349.390424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.390717, "relative_start": 1.411639928817749, "end": 1749848349.390717, "relative_end": 1749848349.390717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.393544, "relative_start": 1.4144668579101562, "end": 1749848349.393544, "relative_end": 1749848349.393544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.396816, "relative_start": 1.417738914489746, "end": 1749848349.396816, "relative_end": 1749848349.396816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.398566, "relative_start": 1.4194889068603516, "end": 1749848349.398566, "relative_end": 1749848349.398566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.406162, "relative_start": 1.4270849227905273, "end": 1749848349.406162, "relative_end": 1749848349.406162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.406715, "relative_start": 1.4276378154754639, "end": 1749848349.406715, "relative_end": 1749848349.406715, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.406968, "relative_start": 1.4278910160064697, "end": 1749848349.406968, "relative_end": 1749848349.406968, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.407435, "relative_start": 1.4283578395843506, "end": 1749848349.407435, "relative_end": 1749848349.407435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.407891, "relative_start": 1.4288139343261719, "end": 1749848349.407891, "relative_end": 1749848349.407891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.408114, "relative_start": 1.4290368556976318, "end": 1749848349.408114, "relative_end": 1749848349.408114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.40834, "relative_start": 1.4292628765106201, "end": 1749848349.40834, "relative_end": 1749848349.40834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.408777, "relative_start": 1.4296998977661133, "end": 1749848349.408777, "relative_end": 1749848349.408777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.409172, "relative_start": 1.4300949573516846, "end": 1749848349.409172, "relative_end": 1749848349.409172, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.409396, "relative_start": 1.430318832397461, "end": 1749848349.409396, "relative_end": 1749848349.409396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.409624, "relative_start": 1.4305469989776611, "end": 1749848349.409624, "relative_end": 1749848349.409624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.41008, "relative_start": 1.4310028553009033, "end": 1749848349.41008, "relative_end": 1749848349.41008, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.410476, "relative_start": 1.431398868560791, "end": 1749848349.410476, "relative_end": 1749848349.410476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.410929, "relative_start": 1.431851863861084, "end": 1749848349.410929, "relative_end": 1749848349.410929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.413961, "relative_start": 1.4348838329315186, "end": 1749848349.413961, "relative_end": 1749848349.413961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.416015, "relative_start": 1.4369378089904785, "end": 1749848349.416015, "relative_end": 1749848349.416015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.417568, "relative_start": 1.438490867614746, "end": 1749848349.417568, "relative_end": 1749848349.417568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.425046, "relative_start": 1.4459688663482666, "end": 1749848349.425046, "relative_end": 1749848349.425046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.425624, "relative_start": 1.4465467929840088, "end": 1749848349.425624, "relative_end": 1749848349.425624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.425903, "relative_start": 1.4468259811401367, "end": 1749848349.425903, "relative_end": 1749848349.425903, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.426553, "relative_start": 1.4474759101867676, "end": 1749848349.426553, "relative_end": 1749848349.426553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.427021, "relative_start": 1.447943925857544, "end": 1749848349.427021, "relative_end": 1749848349.427021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.427262, "relative_start": 1.4481849670410156, "end": 1749848349.427262, "relative_end": 1749848349.427262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.427506, "relative_start": 1.4484288692474365, "end": 1749848349.427506, "relative_end": 1749848349.427506, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.427987, "relative_start": 1.4489099979400635, "end": 1749848349.427987, "relative_end": 1749848349.427987, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.42843, "relative_start": 1.4493529796600342, "end": 1749848349.42843, "relative_end": 1749848349.42843, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.428681, "relative_start": 1.449603796005249, "end": 1749848349.428681, "relative_end": 1749848349.428681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.428916, "relative_start": 1.4498388767242432, "end": 1749848349.428916, "relative_end": 1749848349.428916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.42946, "relative_start": 1.450382947921753, "end": 1749848349.42946, "relative_end": 1749848349.42946, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.429962, "relative_start": 1.4508848190307617, "end": 1749848349.429962, "relative_end": 1749848349.429962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.430253, "relative_start": 1.4511759281158447, "end": 1749848349.430253, "relative_end": 1749848349.430253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.432318, "relative_start": 1.4532408714294434, "end": 1749848349.432318, "relative_end": 1749848349.432318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.434468, "relative_start": 1.4553909301757812, "end": 1749848349.434468, "relative_end": 1749848349.434468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.435908, "relative_start": 1.4568309783935547, "end": 1749848349.435908, "relative_end": 1749848349.435908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.444094, "relative_start": 1.4650168418884277, "end": 1749848349.444094, "relative_end": 1749848349.444094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.44471, "relative_start": 1.4656329154968262, "end": 1749848349.44471, "relative_end": 1749848349.44471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.444979, "relative_start": 1.4659018516540527, "end": 1749848349.444979, "relative_end": 1749848349.444979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.44556, "relative_start": 1.4664828777313232, "end": 1749848349.44556, "relative_end": 1749848349.44556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.44617, "relative_start": 1.4670929908752441, "end": 1749848349.44617, "relative_end": 1749848349.44617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.446459, "relative_start": 1.4673819541931152, "end": 1749848349.446459, "relative_end": 1749848349.446459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.446809, "relative_start": 1.4677319526672363, "end": 1749848349.446809, "relative_end": 1749848349.446809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.447388, "relative_start": 1.468310832977295, "end": 1749848349.447388, "relative_end": 1749848349.447388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.447871, "relative_start": 1.4687938690185547, "end": 1749848349.447871, "relative_end": 1749848349.447871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.448109, "relative_start": 1.469031810760498, "end": 1749848349.448109, "relative_end": 1749848349.448109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.448367, "relative_start": 1.469290018081665, "end": 1749848349.448367, "relative_end": 1749848349.448367, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.448851, "relative_start": 1.4697740077972412, "end": 1749848349.448851, "relative_end": 1749848349.448851, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.449284, "relative_start": 1.4702069759368896, "end": 1749848349.449284, "relative_end": 1749848349.449284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.449591, "relative_start": 1.4705138206481934, "end": 1749848349.449591, "relative_end": 1749848349.449591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.451622, "relative_start": 1.4725449085235596, "end": 1749848349.451622, "relative_end": 1749848349.451622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.453622, "relative_start": 1.4745450019836426, "end": 1749848349.453622, "relative_end": 1749848349.453622, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.455051, "relative_start": 1.4759738445281982, "end": 1749848349.455051, "relative_end": 1749848349.455051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.463374, "relative_start": 1.4842967987060547, "end": 1749848349.463374, "relative_end": 1749848349.463374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.463969, "relative_start": 1.4848918914794922, "end": 1749848349.463969, "relative_end": 1749848349.463969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.464258, "relative_start": 1.4851808547973633, "end": 1749848349.464258, "relative_end": 1749848349.464258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.464765, "relative_start": 1.4856879711151123, "end": 1749848349.464765, "relative_end": 1749848349.464765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.465255, "relative_start": 1.486177921295166, "end": 1749848349.465255, "relative_end": 1749848349.465255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.465518, "relative_start": 1.486440896987915, "end": 1749848349.465518, "relative_end": 1749848349.465518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.46576, "relative_start": 1.4866828918457031, "end": 1749848349.46576, "relative_end": 1749848349.46576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.46621, "relative_start": 1.4871327877044678, "end": 1749848349.46621, "relative_end": 1749848349.46621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.46662, "relative_start": 1.4875428676605225, "end": 1749848349.46662, "relative_end": 1749848349.46662, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.466855, "relative_start": 1.4877779483795166, "end": 1749848349.466855, "relative_end": 1749848349.466855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.467095, "relative_start": 1.4880177974700928, "end": 1749848349.467095, "relative_end": 1749848349.467095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.467581, "relative_start": 1.4885039329528809, "end": 1749848349.467581, "relative_end": 1749848349.467581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.467986, "relative_start": 1.4889090061187744, "end": 1749848349.467986, "relative_end": 1749848349.467986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.468286, "relative_start": 1.4892089366912842, "end": 1749848349.468286, "relative_end": 1749848349.468286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.470323, "relative_start": 1.491245985031128, "end": 1749848349.470323, "relative_end": 1749848349.470323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.472368, "relative_start": 1.493290901184082, "end": 1749848349.472368, "relative_end": 1749848349.472368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.473782, "relative_start": 1.4947049617767334, "end": 1749848349.473782, "relative_end": 1749848349.473782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.485832, "relative_start": 1.5067548751831055, "end": 1749848349.485832, "relative_end": 1749848349.485832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.486441, "relative_start": 1.5073637962341309, "end": 1749848349.486441, "relative_end": 1749848349.486441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.486697, "relative_start": 1.507619857788086, "end": 1749848349.486697, "relative_end": 1749848349.486697, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.487177, "relative_start": 1.5080997943878174, "end": 1749848349.487177, "relative_end": 1749848349.487177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.487641, "relative_start": 1.5085639953613281, "end": 1749848349.487641, "relative_end": 1749848349.487641, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.487869, "relative_start": 1.5087919235229492, "end": 1749848349.487869, "relative_end": 1749848349.487869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.488096, "relative_start": 1.509018898010254, "end": 1749848349.488096, "relative_end": 1749848349.488096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.488541, "relative_start": 1.5094637870788574, "end": 1749848349.488541, "relative_end": 1749848349.488541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.488942, "relative_start": 1.5098648071289062, "end": 1749848349.488942, "relative_end": 1749848349.488942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.489165, "relative_start": 1.5100879669189453, "end": 1749848349.489165, "relative_end": 1749848349.489165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.489398, "relative_start": 1.5103209018707275, "end": 1749848349.489398, "relative_end": 1749848349.489398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.489857, "relative_start": 1.510779857635498, "end": 1749848349.489857, "relative_end": 1749848349.489857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.490263, "relative_start": 1.511185884475708, "end": 1749848349.490263, "relative_end": 1749848349.490263, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.490526, "relative_start": 1.511448860168457, "end": 1749848349.490526, "relative_end": 1749848349.490526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.492826, "relative_start": 1.5137488842010498, "end": 1749848349.492826, "relative_end": 1749848349.492826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.496369, "relative_start": 1.517291784286499, "end": 1749848349.496369, "relative_end": 1749848349.496369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.498192, "relative_start": 1.5191149711608887, "end": 1749848349.498192, "relative_end": 1749848349.498192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.506243, "relative_start": 1.5271658897399902, "end": 1749848349.506243, "relative_end": 1749848349.506243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.506836, "relative_start": 1.5277588367462158, "end": 1749848349.506836, "relative_end": 1749848349.506836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.507108, "relative_start": 1.5280308723449707, "end": 1749848349.507108, "relative_end": 1749848349.507108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.507603, "relative_start": 1.5285258293151855, "end": 1749848349.507603, "relative_end": 1749848349.507603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.508073, "relative_start": 1.5289959907531738, "end": 1749848349.508073, "relative_end": 1749848349.508073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.508315, "relative_start": 1.529237985610962, "end": 1749848349.508315, "relative_end": 1749848349.508315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.508566, "relative_start": 1.5294888019561768, "end": 1749848349.508566, "relative_end": 1749848349.508566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.509017, "relative_start": 1.529939889907837, "end": 1749848349.509017, "relative_end": 1749848349.509017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.509542, "relative_start": 1.5304648876190186, "end": 1749848349.509542, "relative_end": 1749848349.509542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.509803, "relative_start": 1.5307259559631348, "end": 1749848349.509803, "relative_end": 1749848349.509803, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.510045, "relative_start": 1.5309679508209229, "end": 1749848349.510045, "relative_end": 1749848349.510045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.510549, "relative_start": 1.5314719676971436, "end": 1749848349.510549, "relative_end": 1749848349.510549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.510959, "relative_start": 1.5318818092346191, "end": 1749848349.510959, "relative_end": 1749848349.510959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.511236, "relative_start": 1.5321588516235352, "end": 1749848349.511236, "relative_end": 1749848349.511236, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.513928, "relative_start": 1.534850835800171, "end": 1749848349.513928, "relative_end": 1749848349.513928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.516464, "relative_start": 1.5373868942260742, "end": 1749848349.516464, "relative_end": 1749848349.516464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.517871, "relative_start": 1.5387938022613525, "end": 1749848349.517871, "relative_end": 1749848349.517871, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.52545, "relative_start": 1.546372890472412, "end": 1749848349.52545, "relative_end": 1749848349.52545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.526045, "relative_start": 1.5469679832458496, "end": 1749848349.526045, "relative_end": 1749848349.526045, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.526449, "relative_start": 1.5473718643188477, "end": 1749848349.526449, "relative_end": 1749848349.526449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.526952, "relative_start": 1.547874927520752, "end": 1749848349.526952, "relative_end": 1749848349.526952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.527478, "relative_start": 1.54840087890625, "end": 1749848349.527478, "relative_end": 1749848349.527478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.527726, "relative_start": 1.5486488342285156, "end": 1749848349.527726, "relative_end": 1749848349.527726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.527983, "relative_start": 1.548905849456787, "end": 1749848349.527983, "relative_end": 1749848349.527983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.528448, "relative_start": 1.5493710041046143, "end": 1749848349.528448, "relative_end": 1749848349.528448, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.529109, "relative_start": 1.5500319004058838, "end": 1749848349.529109, "relative_end": 1749848349.529109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.529407, "relative_start": 1.5503299236297607, "end": 1749848349.529407, "relative_end": 1749848349.529407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.529717, "relative_start": 1.5506398677825928, "end": 1749848349.529717, "relative_end": 1749848349.529717, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.530483, "relative_start": 1.551405906677246, "end": 1749848349.530483, "relative_end": 1749848349.530483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.530972, "relative_start": 1.5518949031829834, "end": 1749848349.530972, "relative_end": 1749848349.530972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.531268, "relative_start": 1.5521907806396484, "end": 1749848349.531268, "relative_end": 1749848349.531268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.533323, "relative_start": 1.554245948791504, "end": 1749848349.533323, "relative_end": 1749848349.533323, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.535302, "relative_start": 1.5562248229980469, "end": 1749848349.535302, "relative_end": 1749848349.535302, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.536696, "relative_start": 1.5576188564300537, "end": 1749848349.536696, "relative_end": 1749848349.536696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.544066, "relative_start": 1.5649888515472412, "end": 1749848349.544066, "relative_end": 1749848349.544066, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.544631, "relative_start": 1.565553903579712, "end": 1749848349.544631, "relative_end": 1749848349.544631, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.545167, "relative_start": 1.5660898685455322, "end": 1749848349.545167, "relative_end": 1749848349.545167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.545771, "relative_start": 1.5666937828063965, "end": 1749848349.545771, "relative_end": 1749848349.545771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.546429, "relative_start": 1.5673518180847168, "end": 1749848349.546429, "relative_end": 1749848349.546429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.54674, "relative_start": 1.5676629543304443, "end": 1749848349.54674, "relative_end": 1749848349.54674, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.54704, "relative_start": 1.567962884902954, "end": 1749848349.54704, "relative_end": 1749848349.54704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.547564, "relative_start": 1.5684869289398193, "end": 1749848349.547564, "relative_end": 1749848349.547564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.548043, "relative_start": 1.5689659118652344, "end": 1749848349.548043, "relative_end": 1749848349.548043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.548294, "relative_start": 1.5692169666290283, "end": 1749848349.548294, "relative_end": 1749848349.548294, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.548542, "relative_start": 1.569464921951294, "end": 1749848349.548542, "relative_end": 1749848349.548542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.549021, "relative_start": 1.569943904876709, "end": 1749848349.549021, "relative_end": 1749848349.549021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.549465, "relative_start": 1.570387840270996, "end": 1749848349.549465, "relative_end": 1749848349.549465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.549898, "relative_start": 1.5708208084106445, "end": 1749848349.549898, "relative_end": 1749848349.549898, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.553248, "relative_start": 1.5741708278656006, "end": 1749848349.553248, "relative_end": 1749848349.553248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.556536, "relative_start": 1.5774588584899902, "end": 1749848349.556536, "relative_end": 1749848349.556536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.557988, "relative_start": 1.5789108276367188, "end": 1749848349.557988, "relative_end": 1749848349.557988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.566424, "relative_start": 1.5873467922210693, "end": 1749848349.566424, "relative_end": 1749848349.566424, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.567023, "relative_start": 1.5879459381103516, "end": 1749848349.567023, "relative_end": 1749848349.567023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.567284, "relative_start": 1.5882070064544678, "end": 1749848349.567284, "relative_end": 1749848349.567284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.567763, "relative_start": 1.5886859893798828, "end": 1749848349.567763, "relative_end": 1749848349.567763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.568221, "relative_start": 1.589143991470337, "end": 1749848349.568221, "relative_end": 1749848349.568221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.568451, "relative_start": 1.5893738269805908, "end": 1749848349.568451, "relative_end": 1749848349.568451, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.56868, "relative_start": 1.5896029472351074, "end": 1749848349.56868, "relative_end": 1749848349.56868, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.569122, "relative_start": 1.5900449752807617, "end": 1749848349.569122, "relative_end": 1749848349.569122, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.569522, "relative_start": 1.590444803237915, "end": 1749848349.569522, "relative_end": 1749848349.569522, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.569747, "relative_start": 1.590669870376587, "end": 1749848349.569747, "relative_end": 1749848349.569747, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.569974, "relative_start": 1.5908968448638916, "end": 1749848349.569974, "relative_end": 1749848349.569974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.570463, "relative_start": 1.591385841369629, "end": 1749848349.570463, "relative_end": 1749848349.570463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.570863, "relative_start": 1.5917859077453613, "end": 1749848349.570863, "relative_end": 1749848349.570863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.571126, "relative_start": 1.5920488834381104, "end": 1749848349.571126, "relative_end": 1749848349.571126, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.573128, "relative_start": 1.5940508842468262, "end": 1749848349.573128, "relative_end": 1749848349.573128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.575156, "relative_start": 1.596078872680664, "end": 1749848349.575156, "relative_end": 1749848349.575156, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.576642, "relative_start": 1.597564935684204, "end": 1749848349.576642, "relative_end": 1749848349.576642, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.585707, "relative_start": 1.6066298484802246, "end": 1749848349.585707, "relative_end": 1749848349.585707, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.586312, "relative_start": 1.6072349548339844, "end": 1749848349.586312, "relative_end": 1749848349.586312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.586583, "relative_start": 1.6075057983398438, "end": 1749848349.586583, "relative_end": 1749848349.586583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.587068, "relative_start": 1.6079909801483154, "end": 1749848349.587068, "relative_end": 1749848349.587068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.587541, "relative_start": 1.608464002609253, "end": 1749848349.587541, "relative_end": 1749848349.587541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.587783, "relative_start": 1.608705997467041, "end": 1749848349.587783, "relative_end": 1749848349.587783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.588025, "relative_start": 1.608947992324829, "end": 1749848349.588025, "relative_end": 1749848349.588025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.588473, "relative_start": 1.609395980834961, "end": 1749848349.588473, "relative_end": 1749848349.588473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.5889, "relative_start": 1.6098229885101318, "end": 1749848349.5889, "relative_end": 1749848349.5889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.589139, "relative_start": 1.6100618839263916, "end": 1749848349.589139, "relative_end": 1749848349.589139, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.589379, "relative_start": 1.6103019714355469, "end": 1749848349.589379, "relative_end": 1749848349.589379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.589866, "relative_start": 1.6107888221740723, "end": 1749848349.589866, "relative_end": 1749848349.589866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.590275, "relative_start": 1.6111979484558105, "end": 1749848349.590275, "relative_end": 1749848349.590275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.590551, "relative_start": 1.611473798751831, "end": 1749848349.590551, "relative_end": 1749848349.590551, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.592494, "relative_start": 1.6134169101715088, "end": 1749848349.592494, "relative_end": 1749848349.592494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.594678, "relative_start": 1.615600824356079, "end": 1749848349.594678, "relative_end": 1749848349.594678, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.59654, "relative_start": 1.6174628734588623, "end": 1749848349.59654, "relative_end": 1749848349.59654, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.60504, "relative_start": 1.6259629726409912, "end": 1749848349.60504, "relative_end": 1749848349.60504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.605629, "relative_start": 1.626551866531372, "end": 1749848349.605629, "relative_end": 1749848349.605629, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.605929, "relative_start": 1.6268517971038818, "end": 1749848349.605929, "relative_end": 1749848349.605929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.606408, "relative_start": 1.627331018447876, "end": 1749848349.606408, "relative_end": 1749848349.606408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.606938, "relative_start": 1.6278607845306396, "end": 1749848349.606938, "relative_end": 1749848349.606938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.607209, "relative_start": 1.6281318664550781, "end": 1749848349.607209, "relative_end": 1749848349.607209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.60745, "relative_start": 1.6283729076385498, "end": 1749848349.60745, "relative_end": 1749848349.60745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.607907, "relative_start": 1.6288299560546875, "end": 1749848349.607907, "relative_end": 1749848349.607907, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.608378, "relative_start": 1.629300832748413, "end": 1749848349.608378, "relative_end": 1749848349.608378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.608655, "relative_start": 1.629577875137329, "end": 1749848349.608655, "relative_end": 1749848349.608655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.608938, "relative_start": 1.6298608779907227, "end": 1749848349.608938, "relative_end": 1749848349.608938, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.609467, "relative_start": 1.630389928817749, "end": 1749848349.609467, "relative_end": 1749848349.609467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.609908, "relative_start": 1.630831003189087, "end": 1749848349.609908, "relative_end": 1749848349.609908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.610204, "relative_start": 1.631126880645752, "end": 1749848349.610204, "relative_end": 1749848349.610204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.612565, "relative_start": 1.6334879398345947, "end": 1749848349.612565, "relative_end": 1749848349.612565, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.615584, "relative_start": 1.6365067958831787, "end": 1749848349.615584, "relative_end": 1749848349.615584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.617141, "relative_start": 1.638063907623291, "end": 1749848349.617141, "relative_end": 1749848349.617141, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.624655, "relative_start": 1.6455779075622559, "end": 1749848349.624655, "relative_end": 1749848349.624655, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.625249, "relative_start": 1.6461718082427979, "end": 1749848349.625249, "relative_end": 1749848349.625249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.625561, "relative_start": 1.6464838981628418, "end": 1749848349.625561, "relative_end": 1749848349.625561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.626178, "relative_start": 1.6471009254455566, "end": 1749848349.626178, "relative_end": 1749848349.626178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.626652, "relative_start": 1.6475749015808105, "end": 1749848349.626652, "relative_end": 1749848349.626652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.626912, "relative_start": 1.6478350162506104, "end": 1749848349.626912, "relative_end": 1749848349.626912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.627171, "relative_start": 1.6480939388275146, "end": 1749848349.627171, "relative_end": 1749848349.627171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.627646, "relative_start": 1.648568868637085, "end": 1749848349.627646, "relative_end": 1749848349.627646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.628101, "relative_start": 1.6490240097045898, "end": 1749848349.628101, "relative_end": 1749848349.628101, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.628434, "relative_start": 1.6493568420410156, "end": 1749848349.628434, "relative_end": 1749848349.628434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.628713, "relative_start": 1.6496357917785645, "end": 1749848349.628713, "relative_end": 1749848349.628713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.629271, "relative_start": 1.6501939296722412, "end": 1749848349.629271, "relative_end": 1749848349.629271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.629728, "relative_start": 1.650650978088379, "end": 1749848349.629728, "relative_end": 1749848349.629728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.63003, "relative_start": 1.6509528160095215, "end": 1749848349.63003, "relative_end": 1749848349.63003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.632177, "relative_start": 1.6531000137329102, "end": 1749848349.632177, "relative_end": 1749848349.632177, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.635162, "relative_start": 1.6560850143432617, "end": 1749848349.635162, "relative_end": 1749848349.635162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.636733, "relative_start": 1.657655954360962, "end": 1749848349.636733, "relative_end": 1749848349.636733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.644231, "relative_start": 1.665153980255127, "end": 1749848349.644231, "relative_end": 1749848349.644231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.644855, "relative_start": 1.6657779216766357, "end": 1749848349.644855, "relative_end": 1749848349.644855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.645123, "relative_start": 1.666045904159546, "end": 1749848349.645123, "relative_end": 1749848349.645123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.645742, "relative_start": 1.6666648387908936, "end": 1749848349.645742, "relative_end": 1749848349.645742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.646254, "relative_start": 1.6671769618988037, "end": 1749848349.646254, "relative_end": 1749848349.646254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.646736, "relative_start": 1.667658805847168, "end": 1749848349.646736, "relative_end": 1749848349.646736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.647207, "relative_start": 1.6681299209594727, "end": 1749848349.647207, "relative_end": 1749848349.647207, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.647757, "relative_start": 1.66867995262146, "end": 1749848349.647757, "relative_end": 1749848349.647757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.648229, "relative_start": 1.669151782989502, "end": 1749848349.648229, "relative_end": 1749848349.648229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.648468, "relative_start": 1.6693909168243408, "end": 1749848349.648468, "relative_end": 1749848349.648468, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.648702, "relative_start": 1.6696248054504395, "end": 1749848349.648702, "relative_end": 1749848349.648702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.649204, "relative_start": 1.6701269149780273, "end": 1749848349.649204, "relative_end": 1749848349.649204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.649637, "relative_start": 1.6705598831176758, "end": 1749848349.649637, "relative_end": 1749848349.649637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.649941, "relative_start": 1.6708638668060303, "end": 1749848349.649941, "relative_end": 1749848349.649941, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.651936, "relative_start": 1.6728589534759521, "end": 1749848349.651936, "relative_end": 1749848349.651936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.65398, "relative_start": 1.6749029159545898, "end": 1749848349.65398, "relative_end": 1749848349.65398, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.655396, "relative_start": 1.676318883895874, "end": 1749848349.655396, "relative_end": 1749848349.655396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.663706, "relative_start": 1.684628963470459, "end": 1749848349.663706, "relative_end": 1749848349.663706, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.66439, "relative_start": 1.6853129863739014, "end": 1749848349.66439, "relative_end": 1749848349.66439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.664688, "relative_start": 1.6856110095977783, "end": 1749848349.664688, "relative_end": 1749848349.664688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.665227, "relative_start": 1.6861498355865479, "end": 1749848349.665227, "relative_end": 1749848349.665227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.665703, "relative_start": 1.6866259574890137, "end": 1749848349.665703, "relative_end": 1749848349.665703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.66595, "relative_start": 1.686872959136963, "end": 1749848349.66595, "relative_end": 1749848349.66595, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.666213, "relative_start": 1.687135934829712, "end": 1749848349.666213, "relative_end": 1749848349.666213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.666683, "relative_start": 1.687605857849121, "end": 1749848349.666683, "relative_end": 1749848349.666683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.667386, "relative_start": 1.6883089542388916, "end": 1749848349.667386, "relative_end": 1749848349.667386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.667791, "relative_start": 1.688713788986206, "end": 1749848349.667791, "relative_end": 1749848349.667791, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.668095, "relative_start": 1.6890180110931396, "end": 1749848349.668095, "relative_end": 1749848349.668095, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.668638, "relative_start": 1.689560890197754, "end": 1749848349.668638, "relative_end": 1749848349.668638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.669096, "relative_start": 1.690018892288208, "end": 1749848349.669096, "relative_end": 1749848349.669096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.669383, "relative_start": 1.6903059482574463, "end": 1749848349.669383, "relative_end": 1749848349.669383, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.671618, "relative_start": 1.6925408840179443, "end": 1749848349.671618, "relative_end": 1749848349.671618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.673598, "relative_start": 1.6945209503173828, "end": 1749848349.673598, "relative_end": 1749848349.673598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.675009, "relative_start": 1.6959319114685059, "end": 1749848349.675009, "relative_end": 1749848349.675009, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.683676, "relative_start": 1.7045989036560059, "end": 1749848349.683676, "relative_end": 1749848349.683676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.684251, "relative_start": 1.7051739692687988, "end": 1749848349.684251, "relative_end": 1749848349.684251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.684501, "relative_start": 1.7054238319396973, "end": 1749848349.684501, "relative_end": 1749848349.684501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.685157, "relative_start": 1.7060799598693848, "end": 1749848349.685157, "relative_end": 1749848349.685157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.685815, "relative_start": 1.706737995147705, "end": 1749848349.685815, "relative_end": 1749848349.685815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.686128, "relative_start": 1.7070508003234863, "end": 1749848349.686128, "relative_end": 1749848349.686128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.686455, "relative_start": 1.7073779106140137, "end": 1749848349.686455, "relative_end": 1749848349.686455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.687069, "relative_start": 1.7079918384552002, "end": 1749848349.687069, "relative_end": 1749848349.687069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.687639, "relative_start": 1.708561897277832, "end": 1749848349.687639, "relative_end": 1749848349.687639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.68796, "relative_start": 1.7088828086853027, "end": 1749848349.68796, "relative_end": 1749848349.68796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.68831, "relative_start": 1.7092328071594238, "end": 1749848349.68831, "relative_end": 1749848349.68831, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.689033, "relative_start": 1.7099559307098389, "end": 1749848349.689033, "relative_end": 1749848349.689033, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.68962, "relative_start": 1.710542917251587, "end": 1749848349.68962, "relative_end": 1749848349.68962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.689994, "relative_start": 1.7109169960021973, "end": 1749848349.689994, "relative_end": 1749848349.689994, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.69235, "relative_start": 1.7132728099822998, "end": 1749848349.69235, "relative_end": 1749848349.69235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.694434, "relative_start": 1.7153568267822266, "end": 1749848349.694434, "relative_end": 1749848349.694434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.696037, "relative_start": 1.7169599533081055, "end": 1749848349.696037, "relative_end": 1749848349.696037, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.704073, "relative_start": 1.7249958515167236, "end": 1749848349.704073, "relative_end": 1749848349.704073, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.704699, "relative_start": 1.7256219387054443, "end": 1749848349.704699, "relative_end": 1749848349.704699, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.705001, "relative_start": 1.725924015045166, "end": 1749848349.705001, "relative_end": 1749848349.705001, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.705581, "relative_start": 1.726503849029541, "end": 1749848349.705581, "relative_end": 1749848349.705581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.706068, "relative_start": 1.7269909381866455, "end": 1749848349.706068, "relative_end": 1749848349.706068, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.70635, "relative_start": 1.7272729873657227, "end": 1749848349.70635, "relative_end": 1749848349.70635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.70665, "relative_start": 1.7275729179382324, "end": 1749848349.70665, "relative_end": 1749848349.70665, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.707146, "relative_start": 1.7280688285827637, "end": 1749848349.707146, "relative_end": 1749848349.707146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.707588, "relative_start": 1.728510856628418, "end": 1749848349.707588, "relative_end": 1749848349.707588, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.707855, "relative_start": 1.7287778854370117, "end": 1749848349.707855, "relative_end": 1749848349.707855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.708127, "relative_start": 1.7290499210357666, "end": 1749848349.708127, "relative_end": 1749848349.708127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.70862, "relative_start": 1.7295429706573486, "end": 1749848349.70862, "relative_end": 1749848349.70862, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.709052, "relative_start": 1.7299749851226807, "end": 1749848349.709052, "relative_end": 1749848349.709052, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.709365, "relative_start": 1.730287790298462, "end": 1749848349.709365, "relative_end": 1749848349.709365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.711505, "relative_start": 1.7324278354644775, "end": 1749848349.711505, "relative_end": 1749848349.711505, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.714457, "relative_start": 1.735379934310913, "end": 1749848349.714457, "relative_end": 1749848349.714457, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.716038, "relative_start": 1.7369608879089355, "end": 1749848349.716038, "relative_end": 1749848349.716038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.723596, "relative_start": 1.7445189952850342, "end": 1749848349.723596, "relative_end": 1749848349.723596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.724189, "relative_start": 1.7451119422912598, "end": 1749848349.724189, "relative_end": 1749848349.724189, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.724479, "relative_start": 1.7454018592834473, "end": 1749848349.724479, "relative_end": 1749848349.724479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.724977, "relative_start": 1.7458999156951904, "end": 1749848349.724977, "relative_end": 1749848349.724977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.725463, "relative_start": 1.7463858127593994, "end": 1749848349.725463, "relative_end": 1749848349.725463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.725722, "relative_start": 1.7466449737548828, "end": 1749848349.725722, "relative_end": 1749848349.725722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.726024, "relative_start": 1.7469468116760254, "end": 1749848349.726024, "relative_end": 1749848349.726024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.726618, "relative_start": 1.7475409507751465, "end": 1749848349.726618, "relative_end": 1749848349.726618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.72705, "relative_start": 1.7479729652404785, "end": 1749848349.72705, "relative_end": 1749848349.72705, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.727307, "relative_start": 1.74822998046875, "end": 1749848349.727307, "relative_end": 1749848349.727307, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.727569, "relative_start": 1.7484920024871826, "end": 1749848349.727569, "relative_end": 1749848349.727569, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.72804, "relative_start": 1.7489628791809082, "end": 1749848349.72804, "relative_end": 1749848349.72804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.728462, "relative_start": 1.749384880065918, "end": 1749848349.728462, "relative_end": 1749848349.728462, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.728728, "relative_start": 1.7496509552001953, "end": 1749848349.728728, "relative_end": 1749848349.728728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.73329, "relative_start": 1.7542128562927246, "end": 1749848349.73329, "relative_end": 1749848349.73329, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.736335, "relative_start": 1.7572579383850098, "end": 1749848349.736335, "relative_end": 1749848349.736335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.737769, "relative_start": 1.7586917877197266, "end": 1749848349.737769, "relative_end": 1749848349.737769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.747277, "relative_start": 1.7681999206542969, "end": 1749848349.747277, "relative_end": 1749848349.747277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.748026, "relative_start": 1.7689487934112549, "end": 1749848349.748026, "relative_end": 1749848349.748026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.748287, "relative_start": 1.769209861755371, "end": 1749848349.748287, "relative_end": 1749848349.748287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.748904, "relative_start": 1.769826889038086, "end": 1749848349.748904, "relative_end": 1749848349.748904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.749417, "relative_start": 1.7703399658203125, "end": 1749848349.749417, "relative_end": 1749848349.749417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.749679, "relative_start": 1.7706019878387451, "end": 1749848349.749679, "relative_end": 1749848349.749679, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.749923, "relative_start": 1.770845890045166, "end": 1749848349.749923, "relative_end": 1749848349.749923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.750402, "relative_start": 1.771324872970581, "end": 1749848349.750402, "relative_end": 1749848349.750402, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.750849, "relative_start": 1.7717719078063965, "end": 1749848349.750849, "relative_end": 1749848349.750849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.751104, "relative_start": 1.7720270156860352, "end": 1749848349.751104, "relative_end": 1749848349.751104, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.751364, "relative_start": 1.7722868919372559, "end": 1749848349.751364, "relative_end": 1749848349.751364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.751849, "relative_start": 1.7727718353271484, "end": 1749848349.751849, "relative_end": 1749848349.751849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.752275, "relative_start": 1.773197889328003, "end": 1749848349.752275, "relative_end": 1749848349.752275, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.752577, "relative_start": 1.7734999656677246, "end": 1749848349.752577, "relative_end": 1749848349.752577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.754533, "relative_start": 1.7754559516906738, "end": 1749848349.754533, "relative_end": 1749848349.754533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.756515, "relative_start": 1.7774379253387451, "end": 1749848349.756515, "relative_end": 1749848349.756515, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.75792, "relative_start": 1.7788429260253906, "end": 1749848349.75792, "relative_end": 1749848349.75792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.76849, "relative_start": 1.7894129753112793, "end": 1749848349.76849, "relative_end": 1749848349.76849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.769062, "relative_start": 1.789984941482544, "end": 1749848349.769062, "relative_end": 1749848349.769062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.769328, "relative_start": 1.7902510166168213, "end": 1749848349.769328, "relative_end": 1749848349.769328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.769798, "relative_start": 1.7907209396362305, "end": 1749848349.769798, "relative_end": 1749848349.769798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.770268, "relative_start": 1.7911908626556396, "end": 1749848349.770268, "relative_end": 1749848349.770268, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.770537, "relative_start": 1.7914597988128662, "end": 1749848349.770537, "relative_end": 1749848349.770537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.770839, "relative_start": 1.791761875152588, "end": 1749848349.770839, "relative_end": 1749848349.770839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.771334, "relative_start": 1.7922568321228027, "end": 1749848349.771334, "relative_end": 1749848349.771334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.771773, "relative_start": 1.7926959991455078, "end": 1749848349.771773, "relative_end": 1749848349.771773, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.772029, "relative_start": 1.7929518222808838, "end": 1749848349.772029, "relative_end": 1749848349.772029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.772286, "relative_start": 1.7932088375091553, "end": 1749848349.772286, "relative_end": 1749848349.772286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.77286, "relative_start": 1.7937829494476318, "end": 1749848349.77286, "relative_end": 1749848349.77286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.773274, "relative_start": 1.7941968441009521, "end": 1749848349.773274, "relative_end": 1749848349.773274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.773536, "relative_start": 1.7944588661193848, "end": 1749848349.773536, "relative_end": 1749848349.773536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.775828, "relative_start": 1.796750783920288, "end": 1749848349.775828, "relative_end": 1749848349.775828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.778259, "relative_start": 1.7991819381713867, "end": 1749848349.778259, "relative_end": 1749848349.778259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.77998, "relative_start": 1.8009028434753418, "end": 1749848349.77998, "relative_end": 1749848349.77998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.788371, "relative_start": 1.8092939853668213, "end": 1749848349.788371, "relative_end": 1749848349.788371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.788988, "relative_start": 1.8099110126495361, "end": 1749848349.788988, "relative_end": 1749848349.788988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.789254, "relative_start": 1.8101768493652344, "end": 1749848349.789254, "relative_end": 1749848349.789254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.789753, "relative_start": 1.810675859451294, "end": 1749848349.789753, "relative_end": 1749848349.789753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.790215, "relative_start": 1.8111379146575928, "end": 1749848349.790215, "relative_end": 1749848349.790215, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.790454, "relative_start": 1.8113768100738525, "end": 1749848349.790454, "relative_end": 1749848349.790454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.790714, "relative_start": 1.8116369247436523, "end": 1749848349.790714, "relative_end": 1749848349.790714, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.791159, "relative_start": 1.8120818138122559, "end": 1749848349.791159, "relative_end": 1749848349.791159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.791589, "relative_start": 1.812511920928955, "end": 1749848349.791589, "relative_end": 1749848349.791589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.791882, "relative_start": 1.812804937362671, "end": 1749848349.791882, "relative_end": 1749848349.791882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.792157, "relative_start": 1.813079833984375, "end": 1749848349.792157, "relative_end": 1749848349.792157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.79272, "relative_start": 1.813642978668213, "end": 1749848349.79272, "relative_end": 1749848349.79272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.79328, "relative_start": 1.8142027854919434, "end": 1749848349.79328, "relative_end": 1749848349.79328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.793555, "relative_start": 1.8144779205322266, "end": 1749848349.793555, "relative_end": 1749848349.793555, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.795521, "relative_start": 1.816443920135498, "end": 1749848349.795521, "relative_end": 1749848349.795521, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.798136, "relative_start": 1.819058895111084, "end": 1749848349.798136, "relative_end": 1749848349.798136, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.799959, "relative_start": 1.8208818435668945, "end": 1749848349.799959, "relative_end": 1749848349.799959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.807772, "relative_start": 1.8286948204040527, "end": 1749848349.807772, "relative_end": 1749848349.807772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.808368, "relative_start": 1.8292908668518066, "end": 1749848349.808368, "relative_end": 1749848349.808368, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.808636, "relative_start": 1.8295588493347168, "end": 1749848349.808636, "relative_end": 1749848349.808636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.809114, "relative_start": 1.8300368785858154, "end": 1749848349.809114, "relative_end": 1749848349.809114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.809704, "relative_start": 1.8306269645690918, "end": 1749848349.809704, "relative_end": 1749848349.809704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.809954, "relative_start": 1.8308768272399902, "end": 1749848349.809954, "relative_end": 1749848349.809954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.810195, "relative_start": 1.831117868423462, "end": 1749848349.810195, "relative_end": 1749848349.810195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.81064, "relative_start": 1.8315629959106445, "end": 1749848349.81064, "relative_end": 1749848349.81064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.811076, "relative_start": 1.8319988250732422, "end": 1749848349.811076, "relative_end": 1749848349.811076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.811314, "relative_start": 1.8322370052337646, "end": 1749848349.811314, "relative_end": 1749848349.811314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.811566, "relative_start": 1.832489013671875, "end": 1749848349.811566, "relative_end": 1749848349.811566, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.812026, "relative_start": 1.832948923110962, "end": 1749848349.812026, "relative_end": 1749848349.812026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.812428, "relative_start": 1.8333508968353271, "end": 1749848349.812428, "relative_end": 1749848349.812428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.812696, "relative_start": 1.8336188793182373, "end": 1749848349.812696, "relative_end": 1749848349.812696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.816264, "relative_start": 1.8371868133544922, "end": 1749848349.816264, "relative_end": 1749848349.816264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.81874, "relative_start": 1.839662790298462, "end": 1749848349.81874, "relative_end": 1749848349.81874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.820206, "relative_start": 1.8411288261413574, "end": 1749848349.820206, "relative_end": 1749848349.820206, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.828098, "relative_start": 1.8490209579467773, "end": 1749848349.828098, "relative_end": 1749848349.828098, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.828692, "relative_start": 1.8496148586273193, "end": 1749848349.828692, "relative_end": 1749848349.828692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.829032, "relative_start": 1.8499548435211182, "end": 1749848349.829032, "relative_end": 1749848349.829032, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.829598, "relative_start": 1.8505208492279053, "end": 1749848349.829598, "relative_end": 1749848349.829598, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.830089, "relative_start": 1.8510119915008545, "end": 1749848349.830089, "relative_end": 1749848349.830089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.830376, "relative_start": 1.8512988090515137, "end": 1749848349.830376, "relative_end": 1749848349.830376, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.830733, "relative_start": 1.8516559600830078, "end": 1749848349.830733, "relative_end": 1749848349.830733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.831272, "relative_start": 1.8521947860717773, "end": 1749848349.831272, "relative_end": 1749848349.831272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.831725, "relative_start": 1.8526477813720703, "end": 1749848349.831725, "relative_end": 1749848349.831725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.832011, "relative_start": 1.8529338836669922, "end": 1749848349.832011, "relative_end": 1749848349.832011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.832274, "relative_start": 1.8531968593597412, "end": 1749848349.832274, "relative_end": 1749848349.832274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.832767, "relative_start": 1.8536899089813232, "end": 1749848349.832767, "relative_end": 1749848349.832767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.833463, "relative_start": 1.8543858528137207, "end": 1749848349.833463, "relative_end": 1749848349.833463, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.83378, "relative_start": 1.8547029495239258, "end": 1749848349.83378, "relative_end": 1749848349.83378, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.836062, "relative_start": 1.8569848537445068, "end": 1749848349.836062, "relative_end": 1749848349.836062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.838076, "relative_start": 1.8589990139007568, "end": 1749848349.838076, "relative_end": 1749848349.838076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.839493, "relative_start": 1.8604159355163574, "end": 1749848349.839493, "relative_end": 1749848349.839493, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.847765, "relative_start": 1.8686878681182861, "end": 1749848349.847765, "relative_end": 1749848349.847765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.848373, "relative_start": 1.8692958354949951, "end": 1749848349.848373, "relative_end": 1749848349.848373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.848632, "relative_start": 1.8695549964904785, "end": 1749848349.848632, "relative_end": 1749848349.848632, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.849123, "relative_start": 1.8700459003448486, "end": 1749848349.849123, "relative_end": 1749848349.849123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.849586, "relative_start": 1.8705089092254639, "end": 1749848349.849586, "relative_end": 1749848349.849586, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.84991, "relative_start": 1.870832920074463, "end": 1749848349.84991, "relative_end": 1749848349.84991, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.850321, "relative_start": 1.871243953704834, "end": 1749848349.850321, "relative_end": 1749848349.850321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.850835, "relative_start": 1.871757984161377, "end": 1749848349.850835, "relative_end": 1749848349.850835, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.85134, "relative_start": 1.872262954711914, "end": 1749848349.85134, "relative_end": 1749848349.85134, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.851618, "relative_start": 1.8725409507751465, "end": 1749848349.851618, "relative_end": 1749848349.851618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.851894, "relative_start": 1.872816801071167, "end": 1749848349.851894, "relative_end": 1749848349.851894, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.852429, "relative_start": 1.873351812362671, "end": 1749848349.852429, "relative_end": 1749848349.852429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.852861, "relative_start": 1.873783826828003, "end": 1749848349.852861, "relative_end": 1749848349.852861, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.853143, "relative_start": 1.87406587600708, "end": 1749848349.853143, "relative_end": 1749848349.853143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.855173, "relative_start": 1.8760960102081299, "end": 1749848349.855173, "relative_end": 1749848349.855173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.857221, "relative_start": 1.8781437873840332, "end": 1749848349.857221, "relative_end": 1749848349.857221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.858636, "relative_start": 1.879558801651001, "end": 1749848349.858636, "relative_end": 1749848349.858636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.866913, "relative_start": 1.88783597946167, "end": 1749848349.866913, "relative_end": 1749848349.866913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.867533, "relative_start": 1.888455867767334, "end": 1749848349.867533, "relative_end": 1749848349.867533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.867801, "relative_start": 1.8887238502502441, "end": 1749848349.867801, "relative_end": 1749848349.867801, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.868283, "relative_start": 1.8892059326171875, "end": 1749848349.868283, "relative_end": 1749848349.868283, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.868789, "relative_start": 1.889711856842041, "end": 1749848349.868789, "relative_end": 1749848349.868789, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.869014, "relative_start": 1.889936923980713, "end": 1749848349.869014, "relative_end": 1749848349.869014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.869251, "relative_start": 1.8901739120483398, "end": 1749848349.869251, "relative_end": 1749848349.869251, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.869731, "relative_start": 1.8906538486480713, "end": 1749848349.869731, "relative_end": 1749848349.869731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.870178, "relative_start": 1.8911008834838867, "end": 1749848349.870178, "relative_end": 1749848349.870178, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.870416, "relative_start": 1.89133882522583, "end": 1749848349.870416, "relative_end": 1749848349.870416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.870656, "relative_start": 1.8915789127349854, "end": 1749848349.870656, "relative_end": 1749848349.870656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.871153, "relative_start": 1.892076015472412, "end": 1749848349.871153, "relative_end": 1749848349.871153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.871561, "relative_start": 1.8924839496612549, "end": 1749848349.871561, "relative_end": 1749848349.871561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.871836, "relative_start": 1.892758846282959, "end": 1749848349.871836, "relative_end": 1749848349.871836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.8742, "relative_start": 1.89512300491333, "end": 1749848349.8742, "relative_end": 1749848349.8742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.876529, "relative_start": 1.8974518775939941, "end": 1749848349.876529, "relative_end": 1749848349.876529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.878005, "relative_start": 1.898927927017212, "end": 1749848349.878005, "relative_end": 1749848349.878005, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.886325, "relative_start": 1.90724778175354, "end": 1749848349.886325, "relative_end": 1749848349.886325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.886925, "relative_start": 1.9078478813171387, "end": 1749848349.886925, "relative_end": 1749848349.886925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.887203, "relative_start": 1.908125877380371, "end": 1749848349.887203, "relative_end": 1749848349.887203, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.887683, "relative_start": 1.9086058139801025, "end": 1749848349.887683, "relative_end": 1749848349.887683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.888159, "relative_start": 1.9090819358825684, "end": 1749848349.888159, "relative_end": 1749848349.888159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.888401, "relative_start": 1.9093239307403564, "end": 1749848349.888401, "relative_end": 1749848349.888401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.888645, "relative_start": 1.9095678329467773, "end": 1749848349.888645, "relative_end": 1749848349.888645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.889103, "relative_start": 1.9100258350372314, "end": 1749848349.889103, "relative_end": 1749848349.889103, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.889512, "relative_start": 1.9104349613189697, "end": 1749848349.889512, "relative_end": 1749848349.889512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.889749, "relative_start": 1.9106719493865967, "end": 1749848349.889749, "relative_end": 1749848349.889749, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.889996, "relative_start": 1.910918951034546, "end": 1749848349.889996, "relative_end": 1749848349.889996, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.890476, "relative_start": 1.9113988876342773, "end": 1749848349.890476, "relative_end": 1749848349.890476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.890927, "relative_start": 1.9118499755859375, "end": 1749848349.890927, "relative_end": 1749848349.890927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.891195, "relative_start": 1.9121179580688477, "end": 1749848349.891195, "relative_end": 1749848349.891195, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.893192, "relative_start": 1.9141149520874023, "end": 1749848349.893192, "relative_end": 1749848349.893192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.895192, "relative_start": 1.9161148071289062, "end": 1749848349.895192, "relative_end": 1749848349.895192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.897284, "relative_start": 1.9182069301605225, "end": 1749848349.897284, "relative_end": 1749848349.897284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.90562, "relative_start": 1.9265429973602295, "end": 1749848349.90562, "relative_end": 1749848349.90562, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.906225, "relative_start": 1.9271478652954102, "end": 1749848349.906225, "relative_end": 1749848349.906225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.906494, "relative_start": 1.9274168014526367, "end": 1749848349.906494, "relative_end": 1749848349.906494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.906975, "relative_start": 1.9278979301452637, "end": 1749848349.906975, "relative_end": 1749848349.906975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.907441, "relative_start": 1.9283638000488281, "end": 1749848349.907441, "relative_end": 1749848349.907441, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.907683, "relative_start": 1.9286057949066162, "end": 1749848349.907683, "relative_end": 1749848349.907683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.907924, "relative_start": 1.928846836090088, "end": 1749848349.907924, "relative_end": 1749848349.907924, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.90839, "relative_start": 1.9293129444122314, "end": 1749848349.90839, "relative_end": 1749848349.90839, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.908798, "relative_start": 1.9297208786010742, "end": 1749848349.908798, "relative_end": 1749848349.908798, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.909034, "relative_start": 1.9299569129943848, "end": 1749848349.909034, "relative_end": 1749848349.909034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.909274, "relative_start": 1.93019700050354, "end": 1749848349.909274, "relative_end": 1749848349.909274, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.909846, "relative_start": 1.9307689666748047, "end": 1749848349.909846, "relative_end": 1749848349.909846, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.910272, "relative_start": 1.93119478225708, "end": 1749848349.910272, "relative_end": 1749848349.910272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.910541, "relative_start": 1.9314639568328857, "end": 1749848349.910541, "relative_end": 1749848349.910541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.912939, "relative_start": 1.9338619709014893, "end": 1749848349.912939, "relative_end": 1749848349.912939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.91539, "relative_start": 1.9363129138946533, "end": 1749848349.91539, "relative_end": 1749848349.91539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.916866, "relative_start": 1.937788963317871, "end": 1749848349.916866, "relative_end": 1749848349.916866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.924221, "relative_start": 1.9451439380645752, "end": 1749848349.924221, "relative_end": 1749848349.924221, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.924757, "relative_start": 1.9456799030303955, "end": 1749848349.924757, "relative_end": 1749848349.924757, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.925086, "relative_start": 1.9460089206695557, "end": 1749848349.925086, "relative_end": 1749848349.925086, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.925617, "relative_start": 1.9465398788452148, "end": 1749848349.925617, "relative_end": 1749848349.925617, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.926256, "relative_start": 1.947178840637207, "end": 1749848349.926256, "relative_end": 1749848349.926256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.926545, "relative_start": 1.9474678039550781, "end": 1749848349.926545, "relative_end": 1749848349.926545, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.926845, "relative_start": 1.947767972946167, "end": 1749848349.926845, "relative_end": 1749848349.926845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.927664, "relative_start": 1.9485869407653809, "end": 1749848349.927664, "relative_end": 1749848349.927664, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.928114, "relative_start": 1.9490368366241455, "end": 1749848349.928114, "relative_end": 1749848349.928114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.928393, "relative_start": 1.9493157863616943, "end": 1749848349.928393, "relative_end": 1749848349.928393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.928647, "relative_start": 1.9495699405670166, "end": 1749848349.928647, "relative_end": 1749848349.928647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.929149, "relative_start": 1.9500718116760254, "end": 1749848349.929149, "relative_end": 1749848349.929149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.929733, "relative_start": 1.9506559371948242, "end": 1749848349.929733, "relative_end": 1749848349.929733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.93014, "relative_start": 1.9510629177093506, "end": 1749848349.93014, "relative_end": 1749848349.93014, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.932304, "relative_start": 1.9532268047332764, "end": 1749848349.932304, "relative_end": 1749848349.932304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.934304, "relative_start": 1.9552268981933594, "end": 1749848349.934304, "relative_end": 1749848349.934304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.935728, "relative_start": 1.956650972366333, "end": 1749848349.935728, "relative_end": 1749848349.935728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.943328, "relative_start": 1.9642508029937744, "end": 1749848349.943328, "relative_end": 1749848349.943328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.943911, "relative_start": 1.9648339748382568, "end": 1749848349.943911, "relative_end": 1749848349.943911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.944164, "relative_start": 1.9650869369506836, "end": 1749848349.944164, "relative_end": 1749848349.944164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.944676, "relative_start": 1.9655988216400146, "end": 1749848349.944676, "relative_end": 1749848349.944676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.9452, "relative_start": 1.9661228656768799, "end": 1749848349.9452, "relative_end": 1749848349.9452, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.945525, "relative_start": 1.9664478302001953, "end": 1749848349.945525, "relative_end": 1749848349.945525, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.945905, "relative_start": 1.9668278694152832, "end": 1749848349.945905, "relative_end": 1749848349.945905, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.946485, "relative_start": 1.9674079418182373, "end": 1749848349.946485, "relative_end": 1749848349.946485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.947094, "relative_start": 1.9680168628692627, "end": 1749848349.947094, "relative_end": 1749848349.947094, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.947361, "relative_start": 1.9682838916778564, "end": 1749848349.947361, "relative_end": 1749848349.947361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.947606, "relative_start": 1.9685289859771729, "end": 1749848349.947606, "relative_end": 1749848349.947606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.948114, "relative_start": 1.9690368175506592, "end": 1749848349.948114, "relative_end": 1749848349.948114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.948592, "relative_start": 1.9695148468017578, "end": 1749848349.948592, "relative_end": 1749848349.948592, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.948933, "relative_start": 1.969855785369873, "end": 1749848349.948933, "relative_end": 1749848349.948933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.950962, "relative_start": 1.9718849658966064, "end": 1749848349.950962, "relative_end": 1749848349.950962, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.952971, "relative_start": 1.9738938808441162, "end": 1749848349.952971, "relative_end": 1749848349.952971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.954437, "relative_start": 1.9753599166870117, "end": 1749848349.954437, "relative_end": 1749848349.954437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.962162, "relative_start": 1.9830849170684814, "end": 1749848349.962162, "relative_end": 1749848349.962162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.962751, "relative_start": 1.9836738109588623, "end": 1749848349.962751, "relative_end": 1749848349.962751, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.963027, "relative_start": 1.983949899673462, "end": 1749848349.963027, "relative_end": 1749848349.963027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.963623, "relative_start": 1.9845459461212158, "end": 1749848349.963623, "relative_end": 1749848349.963623, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.964112, "relative_start": 1.9850349426269531, "end": 1749848349.964112, "relative_end": 1749848349.964112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.964353, "relative_start": 1.9852759838104248, "end": 1749848349.964353, "relative_end": 1749848349.964353, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.964597, "relative_start": 1.9855198860168457, "end": 1749848349.964597, "relative_end": 1749848349.964597, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.96507, "relative_start": 1.9859929084777832, "end": 1749848349.96507, "relative_end": 1749848349.96507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.965486, "relative_start": 1.9864089488983154, "end": 1749848349.965486, "relative_end": 1749848349.965486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.965711, "relative_start": 1.9866340160369873, "end": 1749848349.965711, "relative_end": 1749848349.965711, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.965939, "relative_start": 1.9868619441986084, "end": 1749848349.965939, "relative_end": 1749848349.965939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.966417, "relative_start": 1.987339973449707, "end": 1749848349.966417, "relative_end": 1749848349.966417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.966915, "relative_start": 1.987837791442871, "end": 1749848349.966915, "relative_end": 1749848349.966915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.967204, "relative_start": 1.9881269931793213, "end": 1749848349.967204, "relative_end": 1749848349.967204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.969243, "relative_start": 1.9901659488677979, "end": 1749848349.969243, "relative_end": 1749848349.969243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.971374, "relative_start": 1.9922969341278076, "end": 1749848349.971374, "relative_end": 1749848349.971374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.972768, "relative_start": 1.9936909675598145, "end": 1749848349.972768, "relative_end": 1749848349.972768, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": 1749848349.981975, "relative_start": 2.0028979778289795, "end": 1749848349.981975, "relative_end": 1749848349.981975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.9826, "relative_start": 2.0035228729248047, "end": 1749848349.9826, "relative_end": 1749848349.9826, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.982889, "relative_start": 2.003811836242676, "end": 1749848349.982889, "relative_end": 1749848349.982889, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.983399, "relative_start": 2.004321813583374, "end": 1749848349.983399, "relative_end": 1749848349.983399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": 1749848349.983951, "relative_start": 2.0048739910125732, "end": 1749848349.983951, "relative_end": 1749848349.983951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.984218, "relative_start": 2.005140781402588, "end": 1749848349.984218, "relative_end": 1749848349.984218, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.984467, "relative_start": 2.005389928817749, "end": 1749848349.984467, "relative_end": 1749848349.984467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.985017, "relative_start": 2.0059399604797363, "end": 1749848349.985017, "relative_end": 1749848349.985017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": 1749848349.985645, "relative_start": 2.00656795501709, "end": 1749848349.985645, "relative_end": 1749848349.985645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": 1749848349.985899, "relative_start": 2.006821870803833, "end": 1749848349.985899, "relative_end": 1749848349.985899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": 1749848349.986148, "relative_start": 2.007071018218994, "end": 1749848349.986148, "relative_end": 1749848349.986148, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": 1749848349.986666, "relative_start": 2.0075888633728027, "end": 1749848349.986666, "relative_end": 1749848349.986666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": 1749848349.987075, "relative_start": 2.007997989654541, "end": 1749848349.987075, "relative_end": 1749848349.987075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": 1749848349.987381, "relative_start": 2.0083038806915283, "end": 1749848349.987381, "relative_end": 1749848349.987381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": 1749848349.989388, "relative_start": 2.0103108882904053, "end": 1749848349.989388, "relative_end": 1749848349.989388, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": 1749848349.991393, "relative_start": 2.0123159885406494, "end": 1749848349.991393, "relative_end": 1749848349.991393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": 1749848349.992906, "relative_start": 2.013828992843628, "end": 1749848349.992906, "relative_end": 1749848349.992906, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.003386, "relative_start": 2.024308919906616, "end": **********.003386, "relative_end": **********.003386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.003978, "relative_start": 2.0249009132385254, "end": **********.003978, "relative_end": **********.003978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.004248, "relative_start": 2.0251708030700684, "end": **********.004248, "relative_end": **********.004248, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.00473, "relative_start": 2.0256528854370117, "end": **********.00473, "relative_end": **********.00473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.005192, "relative_start": 2.0261149406433105, "end": **********.005192, "relative_end": **********.005192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.005428, "relative_start": 2.026350975036621, "end": **********.005428, "relative_end": **********.005428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.005667, "relative_start": 2.026589870452881, "end": **********.005667, "relative_end": **********.005667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.006162, "relative_start": 2.0270848274230957, "end": **********.006162, "relative_end": **********.006162, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.006609, "relative_start": 2.027531862258911, "end": **********.006609, "relative_end": **********.006609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.006866, "relative_start": 2.0277888774871826, "end": **********.006866, "relative_end": **********.006866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.007125, "relative_start": 2.028047800064087, "end": **********.007125, "relative_end": **********.007125, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.007673, "relative_start": 2.0285959243774414, "end": **********.007673, "relative_end": **********.007673, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.008096, "relative_start": 2.0290188789367676, "end": **********.008096, "relative_end": **********.008096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.008369, "relative_start": 2.029291868209839, "end": **********.008369, "relative_end": **********.008369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.010464, "relative_start": 2.0313868522644043, "end": **********.010464, "relative_end": **********.010464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.012533, "relative_start": 2.0334558486938477, "end": **********.012533, "relative_end": **********.012533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.01435, "relative_start": 2.0352728366851807, "end": **********.01435, "relative_end": **********.01435, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.022824, "relative_start": 2.0437469482421875, "end": **********.022824, "relative_end": **********.022824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.023422, "relative_start": 2.044344902038574, "end": **********.023422, "relative_end": **********.023422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.023716, "relative_start": 2.0446388721466064, "end": **********.023716, "relative_end": **********.023716, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.024252, "relative_start": 2.0451748371124268, "end": **********.024252, "relative_end": **********.024252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.024727, "relative_start": 2.045650005340576, "end": **********.024727, "relative_end": **********.024727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.024988, "relative_start": 2.0459108352661133, "end": **********.024988, "relative_end": **********.024988, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.025264, "relative_start": 2.046186923980713, "end": **********.025264, "relative_end": **********.025264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.025742, "relative_start": 2.0466649532318115, "end": **********.025742, "relative_end": **********.025742, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.026211, "relative_start": 2.0471339225769043, "end": **********.026211, "relative_end": **********.026211, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.026508, "relative_start": 2.047430992126465, "end": **********.026508, "relative_end": **********.026508, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.02675, "relative_start": 2.047672986984253, "end": **********.02675, "relative_end": **********.02675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.027225, "relative_start": 2.0481479167938232, "end": **********.027225, "relative_end": **********.027225, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.027636, "relative_start": 2.0485589504241943, "end": **********.027636, "relative_end": **********.027636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.027911, "relative_start": 2.0488338470458984, "end": **********.027911, "relative_end": **********.027911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.030612, "relative_start": 2.05153489112854, "end": **********.030612, "relative_end": **********.030612, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.032824, "relative_start": 2.0537469387054443, "end": **********.032824, "relative_end": **********.032824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.034342, "relative_start": 2.055264949798584, "end": **********.034342, "relative_end": **********.034342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.041872, "relative_start": 2.0627949237823486, "end": **********.041872, "relative_end": **********.041872, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.042485, "relative_start": 2.0634078979492188, "end": **********.042485, "relative_end": **********.042485, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.04284, "relative_start": 2.063762903213501, "end": **********.04284, "relative_end": **********.04284, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.043315, "relative_start": 2.0642378330230713, "end": **********.043315, "relative_end": **********.043315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.043796, "relative_start": 2.0647189617156982, "end": **********.043796, "relative_end": **********.043796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.044034, "relative_start": 2.0649569034576416, "end": **********.044034, "relative_end": **********.044034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.044276, "relative_start": 2.0651988983154297, "end": **********.044276, "relative_end": **********.044276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.045295, "relative_start": 2.0662178993225098, "end": **********.045295, "relative_end": **********.045295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.046071, "relative_start": 2.0669939517974854, "end": **********.046071, "relative_end": **********.046071, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.046446, "relative_start": 2.067368984222412, "end": **********.046446, "relative_end": **********.046446, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.046772, "relative_start": 2.067694902420044, "end": **********.046772, "relative_end": **********.046772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.047786, "relative_start": 2.068708896636963, "end": **********.047786, "relative_end": **********.047786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.048527, "relative_start": 2.0694499015808105, "end": **********.048527, "relative_end": **********.048527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.048935, "relative_start": 2.0698578357696533, "end": **********.048935, "relative_end": **********.048935, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.052331, "relative_start": 2.073253870010376, "end": **********.052331, "relative_end": **********.052331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.055564, "relative_start": 2.076486825942993, "end": **********.055564, "relative_end": **********.055564, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.057056, "relative_start": 2.0779788494110107, "end": **********.057056, "relative_end": **********.057056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.065698, "relative_start": 2.086620807647705, "end": **********.065698, "relative_end": **********.065698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.066321, "relative_start": 2.0872437953948975, "end": **********.066321, "relative_end": **********.066321, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.066591, "relative_start": 2.0875139236450195, "end": **********.066591, "relative_end": **********.066591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.067088, "relative_start": 2.088010787963867, "end": **********.067088, "relative_end": **********.067088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.067571, "relative_start": 2.088493824005127, "end": **********.067571, "relative_end": **********.067571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.067799, "relative_start": 2.088721990585327, "end": **********.067799, "relative_end": **********.067799, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.068026, "relative_start": 2.088948965072632, "end": **********.068026, "relative_end": **********.068026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.068471, "relative_start": 2.0893938541412354, "end": **********.068471, "relative_end": **********.068471, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.068874, "relative_start": 2.089796781539917, "end": **********.068874, "relative_end": **********.068874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.069097, "relative_start": 2.090019941329956, "end": **********.069097, "relative_end": **********.069097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.069322, "relative_start": 2.090245008468628, "end": **********.069322, "relative_end": **********.069322, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.069778, "relative_start": 2.09070086479187, "end": **********.069778, "relative_end": **********.069778, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.070175, "relative_start": 2.091097831726074, "end": **********.070175, "relative_end": **********.070175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.070439, "relative_start": 2.0913619995117188, "end": **********.070439, "relative_end": **********.070439, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.072408, "relative_start": 2.0933308601379395, "end": **********.072408, "relative_end": **********.072408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.074374, "relative_start": 2.095296859741211, "end": **********.074374, "relative_end": **********.074374, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.07577, "relative_start": 2.0966928005218506, "end": **********.07577, "relative_end": **********.07577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.084185, "relative_start": 2.1051077842712402, "end": **********.084185, "relative_end": **********.084185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.084785, "relative_start": 2.105707883834839, "end": **********.084785, "relative_end": **********.084785, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.085056, "relative_start": 2.1059789657592773, "end": **********.085056, "relative_end": **********.085056, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.085558, "relative_start": 2.106480836868286, "end": **********.085558, "relative_end": **********.085558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.086038, "relative_start": 2.1069610118865967, "end": **********.086038, "relative_end": **********.086038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.086264, "relative_start": 2.107186794281006, "end": **********.086264, "relative_end": **********.086264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.086549, "relative_start": 2.1074719429016113, "end": **********.086549, "relative_end": **********.086549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.086993, "relative_start": 2.1079158782958984, "end": **********.086993, "relative_end": **********.086993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.087406, "relative_start": 2.1083288192749023, "end": **********.087406, "relative_end": **********.087406, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.087624, "relative_start": 2.1085469722747803, "end": **********.087624, "relative_end": **********.087624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.087844, "relative_start": 2.108766794204712, "end": **********.087844, "relative_end": **********.087844, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.088314, "relative_start": 2.1092369556427, "end": **********.088314, "relative_end": **********.088314, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.088704, "relative_start": 2.1096270084381104, "end": **********.088704, "relative_end": **********.088704, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.088957, "relative_start": 2.109879970550537, "end": **********.088957, "relative_end": **********.088957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.091012, "relative_start": 2.1119349002838135, "end": **********.091012, "relative_end": **********.091012, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.093226, "relative_start": 2.1141488552093506, "end": **********.093226, "relative_end": **********.093226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.09473, "relative_start": 2.1156527996063232, "end": **********.09473, "relative_end": **********.09473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.102748, "relative_start": 2.123670816421509, "end": **********.102748, "relative_end": **********.102748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.103344, "relative_start": 2.1242668628692627, "end": **********.103344, "relative_end": **********.103344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.103613, "relative_start": 2.1245357990264893, "end": **********.103613, "relative_end": **********.103613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.104114, "relative_start": 2.1250369548797607, "end": **********.104114, "relative_end": **********.104114, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.104578, "relative_start": 2.1255009174346924, "end": **********.104578, "relative_end": **********.104578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.104818, "relative_start": 2.1257410049438477, "end": **********.104818, "relative_end": **********.104818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.105081, "relative_start": 2.1260039806365967, "end": **********.105081, "relative_end": **********.105081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.105528, "relative_start": 2.126451015472412, "end": **********.105528, "relative_end": **********.105528, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.105936, "relative_start": 2.126858949661255, "end": **********.105936, "relative_end": **********.105936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.106174, "relative_start": 2.1270968914031982, "end": **********.106174, "relative_end": **********.106174, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.106416, "relative_start": 2.1273388862609863, "end": **********.106416, "relative_end": **********.106416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.106917, "relative_start": 2.1278398036956787, "end": **********.106917, "relative_end": **********.106917, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.107339, "relative_start": 2.1282618045806885, "end": **********.107339, "relative_end": **********.107339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.107624, "relative_start": 2.128546953201294, "end": **********.107624, "relative_end": **********.107624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.110303, "relative_start": 2.131225824356079, "end": **********.110303, "relative_end": **********.110303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.112663, "relative_start": 2.1335859298706055, "end": **********.112663, "relative_end": **********.112663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.114379, "relative_start": 2.1353018283843994, "end": **********.114379, "relative_end": **********.114379, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.122386, "relative_start": 2.1433088779449463, "end": **********.122386, "relative_end": **********.122386, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.12298, "relative_start": 2.1439030170440674, "end": **********.12298, "relative_end": **********.12298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.12327, "relative_start": 2.144192934036255, "end": **********.12327, "relative_end": **********.12327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.12375, "relative_start": 2.1446728706359863, "end": **********.12375, "relative_end": **********.12375, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0512b35c86f8e0c87bcf0424bc39a7cf", "start": **********.124239, "relative_start": 2.1451618671417236, "end": **********.124239, "relative_end": **********.124239, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.124489, "relative_start": 2.145411968231201, "end": **********.124489, "relative_end": **********.124489, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.124733, "relative_start": 2.145655870437622, "end": **********.124733, "relative_end": **********.124733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.125179, "relative_start": 2.146101951599121, "end": **********.125179, "relative_end": **********.125179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.125607, "relative_start": 2.1465299129486084, "end": **********.125607, "relative_end": **********.125607, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.125845, "relative_start": 2.1467678546905518, "end": **********.125845, "relative_end": **********.125845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.126235, "relative_start": 2.147157907485962, "end": **********.126235, "relative_end": **********.126235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.126776, "relative_start": 2.1476988792419434, "end": **********.126776, "relative_end": **********.126776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.127184, "relative_start": 2.148106813430786, "end": **********.127184, "relative_end": **********.127184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.127479, "relative_start": 2.148401975631714, "end": **********.127479, "relative_end": **********.127479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.129601, "relative_start": 2.1505239009857178, "end": **********.129601, "relative_end": **********.129601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.132701, "relative_start": 2.1536238193511963, "end": **********.132701, "relative_end": **********.132701, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.13426, "relative_start": 2.1551828384399414, "end": **********.13426, "relative_end": **********.13426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.140637, "relative_start": 2.161559820175171, "end": **********.141036, "relative_end": **********.141036, "duration": 0.0003991127014160156, "duration_str": "399μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 57516392, "peak_usage_str": "55MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 831, "nb_templates": 831, "templates": [{"name": "1x core/table::bulk-changes", "param_count": null, "params": [], "start": **********.844202, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::bulk-changes"}, {"name": "4x a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.847944, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 4, "name_original": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item"}, {"name": "1x __components::b3952f2f8079cae92614235bd0a03d56", "param_count": null, "params": [], "start": **********.851184, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b3952f2f8079cae92614235bd0a03d56.blade.php__components::b3952f2f8079cae92614235bd0a03d56", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb3952f2f8079cae92614235bd0a03d56.blade.php&line=1", "ajax": false, "filename": "b3952f2f8079cae92614235bd0a03d56.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::b3952f2f8079cae92614235bd0a03d56"}, {"name": "1x core/table::partials.create", "param_count": null, "params": [], "start": **********.862441, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/partials/create.blade.phpcore/table::partials.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::partials.create"}, {"name": "1x __components::21a9e4f6a6bd57aad5711446b399a39c", "param_count": null, "params": [], "start": **********.863896, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/21a9e4f6a6bd57aad5711446b399a39c.blade.php__components::21a9e4f6a6bd57aad5711446b399a39c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F21a9e4f6a6bd57aad5711446b399a39c.blade.php&line=1", "ajax": false, "filename": "21a9e4f6a6bd57aad5711446b399a39c.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::21a9e4f6a6bd57aad5711446b399a39c"}, {"name": "2x core/table::includes.header-action", "param_count": null, "params": [], "start": **********.865578, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/includes/header-action.blade.phpcore/table::includes.header-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fincludes%2Fheader-action.blade.php&line=1", "ajax": false, "filename": "header-action.blade.php", "line": "?"}, "render_count": 2, "name_original": "core/table::includes.header-action"}, {"name": "1x __components::60cb3c84e43d8fd13d066b6332a243d5", "param_count": null, "params": [], "start": **********.866639, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/60cb3c84e43d8fd13d066b6332a243d5.blade.php__components::60cb3c84e43d8fd13d066b6332a243d5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F60cb3c84e43d8fd13d066b6332a243d5.blade.php&line=1", "ajax": false, "filename": "60cb3c84e43d8fd13d066b6332a243d5.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::60cb3c84e43d8fd13d066b6332a243d5"}, {"name": "1x __components::5d51c9333cac7b48f699a15fa5950d60", "param_count": null, "params": [], "start": **********.867946, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5d51c9333cac7b48f699a15fa5950d60.blade.php__components::5d51c9333cac7b48f699a15fa5950d60", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5d51c9333cac7b48f699a15fa5950d60.blade.php&line=1", "ajax": false, "filename": "5d51c9333cac7b48f699a15fa5950d60.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::5d51c9333cac7b48f699a15fa5950d60"}, {"name": "1x core/table::table-info", "param_count": null, "params": [], "start": **********.869741, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}, "render_count": 1, "name_original": "core/table::table-info"}, {"name": "1x __components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.870788, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::0343a1b0800146d7d9cf6a9514ec7bf4"}, {"name": "1x a74ad8dfacd4f985eb3977517615ce25::badge", "param_count": null, "params": [], "start": **********.871242, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/badge.blade.phpa74ad8dfacd4f985eb3977517615ce25::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}, "render_count": 1, "name_original": "a74ad8dfacd4f985eb3977517615ce25::badge"}, {"name": "48x core/table::row-actions", "param_count": null, "params": [], "start": 1749848349.141504, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/row-actions.blade.phpcore/table::row-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Frow-actions.blade.php&line=1", "ajax": false, "filename": "row-actions.blade.php", "line": "?"}, "render_count": 48, "name_original": "core/table::row-actions"}, {"name": "144x core/table::actions.action", "param_count": null, "params": [], "start": 1749848349.142278, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/action.blade.phpcore/table::actions.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}, "render_count": 144, "name_original": "core/table::actions.action"}, {"name": "144x core/table::actions.includes.action-attributes", "param_count": null, "params": [], "start": 1749848349.142783, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/includes/action-attributes.blade.phpcore/table::actions.includes.action-attributes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-attributes.blade.php&line=1", "ajax": false, "filename": "action-attributes.blade.php", "line": "?"}, "render_count": 144, "name_original": "core/table::actions.includes.action-attributes"}, {"name": "144x core/table::actions.includes.action-icon", "param_count": null, "params": [], "start": 1749848349.143588, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/includes/action-icon.blade.phpcore/table::actions.includes.action-icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-icon.blade.php&line=1", "ajax": false, "filename": "action-icon.blade.php", "line": "?"}, "render_count": 144, "name_original": "core/table::actions.includes.action-icon"}, {"name": "48x __components::0512b35c86f8e0c87bcf0424bc39a7cf", "param_count": null, "params": [], "start": 1749848349.144607, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0512b35c86f8e0c87bcf0424bc39a7cf.blade.php__components::0512b35c86f8e0c87bcf0424bc39a7cf", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0512b35c86f8e0c87bcf0424bc39a7cf.blade.php&line=1", "ajax": false, "filename": "0512b35c86f8e0c87bcf0424bc39a7cf.blade.php", "line": "?"}, "render_count": 48, "name_original": "__components::0512b35c86f8e0c87bcf0424bc39a7cf"}, {"name": "48x __components::956251f2dec11412ac25a26a9f7d71fa", "param_count": null, "params": [], "start": 1749848349.147455, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/956251f2dec11412ac25a26a9f7d71fa.blade.php__components::956251f2dec11412ac25a26a9f7d71fa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F956251f2dec11412ac25a26a9f7d71fa.blade.php&line=1", "ajax": false, "filename": "956251f2dec11412ac25a26a9f7d71fa.blade.php", "line": "?"}, "render_count": 48, "name_original": "__components::956251f2dec11412ac25a26a9f7d71fa"}, {"name": "48x __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "param_count": null, "params": [], "start": 1749848349.150714, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/151f0fb0c0a10839cb1d6b3ad5c9290f.blade.php__components::151f0fb0c0a10839cb1d6b3ad5c9290f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F151f0fb0c0a10839cb1d6b3ad5c9290f.blade.php&line=1", "ajax": false, "filename": "151f0fb0c0a10839cb1d6b3ad5c9290f.blade.php", "line": "?"}, "render_count": 48, "name_original": "__components::151f0fb0c0a10839cb1d6b3ad5c9290f"}, {"name": "48x plugins/language-advanced::language-column", "param_count": null, "params": [], "start": 1749848349.151499, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/language-advanced/resources/views/language-column.blade.phpplugins/language-advanced::language-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fresources%2Fviews%2Flanguage-column.blade.php&line=1", "ajax": false, "filename": "language-column.blade.php", "line": "?"}, "render_count": 48, "name_original": "plugins/language-advanced::language-column"}, {"name": "48x __components::ab1a4d173a14198db45d9196164e988b", "param_count": null, "params": [], "start": 1749848349.154504, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ab1a4d173a14198db45d9196164e988b.blade.php__components::ab1a4d173a14198db45d9196164e988b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fab1a4d173a14198db45d9196164e988b.blade.php&line=1", "ajax": false, "filename": "ab1a4d173a14198db45d9196164e988b.blade.php", "line": "?"}, "render_count": 48, "name_original": "__components::ab1a4d173a14198db45d9196164e988b"}, {"name": "48x __components::7acd10aaf307836f141c823026de3629", "param_count": null, "params": [], "start": 1749848349.156878, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/7acd10aaf307836f141c823026de3629.blade.php__components::7acd10aaf307836f141c823026de3629", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F7acd10aaf307836f141c823026de3629.blade.php&line=1", "ajax": false, "filename": "7acd10aaf307836f141c823026de3629.blade.php", "line": "?"}, "render_count": 48, "name_original": "__components::7acd10aaf307836f141c823026de3629"}, {"name": "48x core/table::partials.checkbox", "param_count": null, "params": [], "start": 1749848349.163056, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/partials/checkbox.blade.phpcore/table::partials.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}, "render_count": 48, "name_original": "core/table::partials.checkbox"}]}, "queries": {"count": 10, "nb_statements": 10, "nb_visible_statements": 10, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.19372, "accumulated_duration_str": "194ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.8009288, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.212}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.808771, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.212, "width_percent": 0.212}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 230}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 275}], "start": **********.8813, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.423, "width_percent": 0.253}, {"sql": "select `lang_code`, `lang_name`, `lang_flag` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 1184}, {"index": 19, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 238}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.884519, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.676, "width_percent": 0.176}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 81}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 238}], "start": **********.886703, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.852, "width_percent": 0.155}, {"sql": "select count(*) as aggregate from `mp_stores`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 707}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 148}, {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 20, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}], "start": **********.896245, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:165", "source": {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=165", "ajax": false, "filename": "QueryDataTable.php", "line": "165"}, "connection": "muhrak", "explain": null, "start_percent": 1.007, "width_percent": 0.17}, {"sql": "select `id`, `logo`, `name`, `created_at`, `status`, `customer_id`, (select count(*) from `ec_products` where `mp_stores`.`id` = `ec_products`.`store_id`) as `products_count` from `mp_stores` order by `id` desc limit 100 offset 300", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 137}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 18, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}, {"index": 19, "namespace": null, "name": "platform/plugins/marketplace/src/Tables/StoreTable.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\marketplace\\src\\Tables\\StoreTable.php", "line": 65}], "start": **********.897994, "duration": 0.18872999999999998, "duration_str": "189ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.177, "width_percent": 97.424}, {"sql": "select * from `ec_customers` where `ec_customers`.`id` in (65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 137}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 24, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}], "start": 1749848349.091085, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 98.601, "width_percent": 0.547}, {"sql": "select * from `mp_vendor_info` where `mp_vendor_info`.`customer_id` in (65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 28, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 137}, {"index": 29, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}], "start": 1749848349.1145792, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 99.148, "width_percent": 0.594}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": 1749848349.159089, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 99.742, "width_percent": 0.258}]}, "models": {"data": {"Botble\\Marketplace\\Models\\Store": {"value": 48, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FStore.php&line=1", "ajax": false, "filename": "Store.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Customer": {"value": 48, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCustomer.php&line=1", "ajax": false, "filename": "Customer.php", "line": "?"}}, "Botble\\Marketplace\\Models\\VendorInfo": {"value": 48, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FModels%2FVendorInfo.php&line=1", "ajax": false, "filename": "VendorInfo.php", "line": "?"}}, "Botble\\Language\\Models\\Language": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 157, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores", "action_name": "marketplace.store.index", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@index", "uri": "GET admin/marketplaces/stores", "controller": "Botble\\Marketplace\\Http\\Controllers\\StoreController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=36\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Marketplace\\Http\\Controllers", "prefix": "admin/marketplaces/stores", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fmarketplace%2Fsrc%2FHttp%2FControllers%2FStoreController.php&line=36\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/marketplace/src/Http/Controllers/StoreController.php:36-41</a>", "middleware": "web, core, auth", "duration": "2.17s", "peak_memory": "58MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1351166787 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1351166787\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-903796606 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str>5</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">logo</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">logo</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">earnings</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">earnings</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products_count</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"14 characters\">products_count</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer_name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">customer_name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">language</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">language_meta.lang_meta_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str title=\"3 characters\">300</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"3 characters\">100</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-903796606\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-570820395 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2604</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ikt1VjdFaGZoY2ZibEhjeGcycHBRZ2c9PSIsInZhbHVlIjoib3JHekx5NElFNnNURnU5K2VadzVYVHVpenJYNmI0RmVHc0V4eGNWbkZxcE5uWUFESTRDYXJkUXd5RzRFYlRXOTlDMnlBanprckgvNG1SZWNKYk5TVWN3UnNpT0tWd1VZUklIODY5b2dtbjIvTDR1NGdOZVkwdVNYYkxYZ1d5SHQiLCJtYWMiOiJiYTQ0OWU0ODEzMzFlZmE5ZDg2MmQxM2QxYjNhYWU5NmZkZjhkNGU0NGJmMGQ2ZWYzMjg4N2NlZWQ3MWIxYjMxIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6Ilh5ZS9NcTRqSUt2QlorMk9zbUNXYXc9PSIsInZhbHVlIjoiaStSYnV4T2cvZTVpOTdIYzRPM0w0V0xjUm8xZTNiMjB6NStyUVMrck1oWkV6STd6UDlKWmlEWDlzbHU5WTVzNndwSGhEdjFJdkdNV0FVNE1MeXgrYVZDd0tINkF5UUNxZGFsQzdWOUtYNVV1ZHdIMWdocFhCQ3JXVTFMYWlMei8iLCJtYWMiOiJiNzcyZGVjYTQ2NDI1YTdmZTM3MGM4NGRiYTBkZGMyZDVmY2IyYzc4NzRiMTdlZTBhMmRlNzc2ZjYyMmMyN2NhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-570820395\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 20:59:10 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-514389558 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"43 characters\">https://muhrak.gc/admin/marketplaces/stores</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-514389558\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/marketplaces/stores", "action_name": "marketplace.store.index", "controller_action": "Botble\\Marketplace\\Http\\Controllers\\StoreController@index"}, "badge": null}}