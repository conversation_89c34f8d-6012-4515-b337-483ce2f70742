<?php

namespace Bo<PERSON>ble\Marketplace\Providers;

use Botble\Base\Supports\ServiceProvider;
use Botble\Marketplace\Commands\CreateVendorAccountsCommand;

class CommandServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        if (! $this->app->runningInConsole()) {
            return;
        }

        $this->commands([
            CreateVendorAccountsCommand::class,
        ]);
    }
}
