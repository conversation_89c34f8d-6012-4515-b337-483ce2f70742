<div class="ps-page--single ps-page--vendor">
    <section class="ps-store-list">
        <div class="container">
            {{-- @php $coverImage = $store->getMetaData('cover_image', true); @endphp
            {!! RvMedia::image($coverImage, $store->name, 'full', true , ['class'=> 'cover-image','height'=> '230']) !!} --}}
            <aside class="ps-block--store-banner">
                <div class="ps-block__user">
                    <div class="ps-block__user-avatar">
                        <img src="{{ $store->logo_url }}" alt="{{ $store->name }}">
                        {{-- @if (EcommerceHelper::isReviewEnabled())
                            <div class="rating_wrap">
                                <div class="rating">
                                    <div class="product_rate" style="width: {{ $store->reviews()->avg('star') * 20 }}%"></div>
                                </div>
                                <span class="rating_num">({{ $store->reviews()->count() }})</span>
                            </div>
                        @endif --}}
                    </div>
                    <div class="ps-block__user-content">
                        <h3>{{ $store->name }} <span>

                                <svg fill="#0b2fa2" version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg"
                                    xmlns:xlink="http://www.w3.org/1999/xlink" width="20px" height="20px"
                                    viewBox="0 0 536.541 536.541"xml:space="preserve">
                                    <g>
                                        <g>
                                            <path d="M496.785,152.779c-3.305-25.085-16.549-51.934-38.826-74.205c-22.264-22.265-49.107-35.508-74.186-38.813
                                                c-11.348-1.499-26.5-7.766-35.582-14.737C328.111,9.626,299.764,0,268.27,0s-59.841,9.626-79.921,25.024
                                                c-9.082,6.965-24.235,13.238-35.582,14.737c-25.08,3.305-51.922,16.549-74.187,38.813c-22.277,22.271-35.521,49.119-38.825,74.205
                                                c-1.493,11.347-7.766,26.494-14.731,35.57C9.621,208.422,0,236.776,0,268.27s9.621,59.847,25.024,79.921
                                                c6.971,9.082,13.238,24.223,14.731,35.568c3.305,25.086,16.548,51.936,38.825,74.205c22.265,22.266,49.107,35.51,74.187,38.814
                                                c11.347,1.498,26.5,7.771,35.582,14.736c20.073,15.398,48.421,25.025,79.921,25.025s59.841-9.627,79.921-25.025
                                                c9.082-6.965,24.234-13.238,35.582-14.736c25.078-3.305,51.922-16.549,74.186-38.814c22.277-22.27,35.521-49.119,38.826-74.205
                                                c1.492-11.346,7.766-26.492,14.73-35.568c15.404-20.074,25.025-48.422,25.025-79.921c0-31.494-9.621-59.848-25.025-79.921
                                                C504.545,179.273,498.277,164.126,496.785,152.779z M439.256,180.43L246.477,373.209l-30.845,30.846
                                                c-8.519,8.52-22.326,8.52-30.845,0l-30.845-30.846l-56.665-56.658c-8.519-8.52-8.519-22.326,0-30.846l30.845-30.844
                                                c8.519-8.519,22.326-8.519,30.845,0l41.237,41.236L377.561,118.74c8.52-8.519,22.326-8.519,30.846,0l30.844,30.845
                                                C447.775,158.104,447.775,171.917,439.256,180.43z" />
                                        </g>
                                    </g>
                                </svg>
                            </span></h3>
                        {{-- @if (!MarketplaceHelper::hideStoreAddress() && $store->full_address)
                            <p><i class="icon-map-marker" ></i>&nbsp;{{ $store->full_address }}</p>
                        @endif --}}
                        @php
                            $description = BaseHelper::clean($store->description);
                        @endphp

                        @if ($description)
                            <p class="store-description">{!! $description !!}</p>
                        @endif

                        {{-- @if (!MarketplaceHelper::hideStorePhoneNumber() && $store->phone)
                            <p><i class="icon-telephone" ></i>&nbsp;{{ $store->phone }}</p>
                        @endif
                        @if (!MarketplaceHelper::hideStoreEmail() && $store->email)
                            <p><i class="icon-envelope" ></i>&nbsp;<a href="mailto:{{ $store->email }}">{{ $store->email }}</a></p>
                        @endif --}}

                        <div class="ps-block-buttons">
                            <a href="#" class="ps-btn">Inquiry</a>
                            <a href="#" class="ps-btn ps-btn--outline">How to buy</a>
                        </div>

                    </div>
                </div>
                @if ($store->categories->count())
                <div>
                    <div class="d-flex gap-3 categories-tag store-page-tag">
                        @foreach($store->categories as $category)
                            <a href="{{ $category->url }}" class="btn-tag" >{!! BaseHelper::clean($category->name) !!}</a>
                        @endforeach
                    </div>
                </div>
                @endif
                {{-- @php
                    $description = BaseHelper::clean($store->description);
                @endphp

                @if ($description || $content)
                    <div class="py-4 mb-4 bg-light">
                        <div class="px-4">
                            <div id="store-short-description">
                                {!! $description ?: Str::limit($content, 250) !!}
                            </div>
                        </div>
                    </div>
                @endif --}}
            </aside>
        </div>
        <div class="container-fluid">
            <div class="ps-section__wrapper">
                @if ( $canContactStore =  MarketplaceHelper::isEnabledMessagingSystem() && (!auth('customer')->check() || $store->id != auth('customer')->user()->store->id))
                    {{-- <div class="ps-layout--shop"> --}}
                        {{-- <div class="ps-layout__left">
                                <div class="store-contact-form mb-4 bg-light p-4">
                                    <h3 class="fs-4">{{ __('Email :store', ['store' => $store->name]) }}</h3>
                                    <p>{{ __('All messages are recorded and spam is not tolerated. Your email address will be shown to the recipient.') }}</p>
                                    {!!
                                        $contactForm
                                        ->setFormOption('class', 'ps-form--contact-us contact-form bb-contact-store-form')
                                        ->setFormInputClass('form-control')
                                        ->setFormLabelClass('d-none sr-only')
                                        ->modify(
                                            'submit',
                                            'submit',
                                            Botble\Base\Forms\FieldOptions\ButtonFieldOption::make()
                                                ->addAttribute('data-bb-loading', 'button-loading')
                                                ->cssClass('ps-btn')
                                                ->label(__('Send message'))
                                                ->wrapperAttributes(['class' => 'form-group submit'])
                                                ->toArray(),
                                            true
                                        )
                                        ->renderForm()
                                    !!}
                            </div>

                            @include(MarketplaceHelper::viewPath('includes.contact-form-script'))
                    </div> --}}
                        {{-- <div class="ps-layout__right"> --}}
                @endif
                <div class="ps-shopping ps-tab-root">

                    <div class="ps-shopping__header ps-store-tab-list">

                        <div class="container ps-shopping__actions">
                            <div class="ps-shopping__view">
                                <ul class="ps-tab-list">
                                    <li class="active"><a href="#tab-1"> {{ __('BizHome') }}</a></li>
                                    <li><a href="#tab-2">{{ __('Product List') }}</a></li>
                                    <li><a href="#tab-3">{{ __('Data Rooms') }}</a></li>
                                    <li><a href="#tab-4">{{ __('Distributor List') }}</a></li>
                                    <li><a href="#tab-5">{{ __('Model List') }}</a></li>
                                </ul>
                            </div>
                        </div>

                    </div>
                    <div class="container ps-tabs">


                                <div class="ps-tab active" id="tab-1">
                                    <div class="row">
                                    <div class="col-lg-8 col-md-8 col-sm-12">
                                        <div class="content my-card">
                                            {!! BaseHelper::clean($store->content) !!}
                                        </div>

                                        <div class="my-card">
                                            <h4>Location</h4>
                                            @php

                                            @endphp
                                            @if ($store->address)
                                                <p><i class="icon-map-marker"></i> {{ $store->full_address }}</p>
                                            @endif
                                            <iframe width="100%" class="w-full h-full" src="https://maps.google.com/maps?q={{ urlencode($store->full_address) }}%20&t=&z=13&ie=UTF8&iwloc=&output=embed" frameborder="0" scrolling="no" marginheight="0" marginwidth="0"></iframe>
                                        </div>

                                    </div>
                                    <div class="col-lg-4 col-md-4 col-sm-12">
                                        <div class="my-card ps-block--store-info">
                                            <div class="ps-block__header">
                                                <h3>{{ __('Store Information') }}</h3>
                                            </div>
                                            <div class="ps-block__content">
                                                <ul class="list-unstyled ps-list--store-info">

                                                    @if ($store->phone)
                                                        <li><i class="fa fa-phone"></i> <a href="tel:{{ $store->phone }}">{{ $store->phone }}</a></li>
                                                    @endif
                                                    @if ($store->email)
                                                        <li><i class="fa fa-envelope"></i> <a href="mailto:{{ $store->email }}">{{ $store->email }}</a></li>
                                                    @endif
                                                    @if ($store->address)
                                                        <li><i class="fa fa-map-marker"></i> {{ $store->full_address }}</li>
                                                    @endif
                                                </ul>
                                                @if (!MarketplaceHelper::hideStoreSocialLinks() && ($socials = $store->getMetaData('social_links', true)))
                                                <div class="ps-block__social">
                                                    @foreach (MarketplaceHelper::getAllowedSocialLinks() as $key => $social)
                                                        @continue(! Arr::get($socials, $key))
                                                        <a href="{{ Arr::get($social, 'url') . Arr::get($socials, $key) }}" target="_blank" title="{{ Arr::get($social, 'title') }}">
                                                            @if ($icon = Arr::get($social, 'icon'))
                                                                {{-- <i class="fa fa-{{ $icon }}"></i> --}}
                                                                <x-core::icon :name="'ti ti-brand-' . $icon" />
                                                            @endif
                                                        </a>
                                                    @endforeach
                                                </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    </div>

                                </div>

                                <div class="ps-tab" id="tab-2">
                                    <div class="ps-section__search">
                                        <div class="mb-3">
                                            <form class="products-filter-form-vendor" action="{{ URL::current() }}"
                                                method="GET">
                                                <div class="form-group mb-5">
                                                    <button><i class="icon-magnifier"></i></button>
                                                    <input class="form-control" name="q"
                                                        value="{{ BaseHelper::stringify(request()->query('q')) }}"
                                                        type="text" placeholder="{{ __('Search in this store...') }}">
                                                </div>
                                            </form>
                                        </div>
                                    </div>
                                    <p><strong> {{ $products->total() }}</strong> {{ __('Products found') }}</p>
                                    <div class="ps-shopping-product">
                                        <div class="row">
                                            @if ($products->isNotEmpty())
                                                @foreach ($products as $product)
                                                    <div class="col-xl-3 col-lg-3 col-md-4 col-sm-6 col-6 ">
                                                        <div class="ps-product">
                                                            {!! Theme::partial('product-item', compact('product')) !!}
                                                        </div>
                                                    </div>
                                                @endforeach
                                            @endif
                                        </div>
                                    </div>
                                    <div class="ps-pagination">
                                        {!! $products->withQueryString()->links() !!}
                                    </div>
                                </div>
                                <div class="ps-tab" id="tab-3">
                                    @if ($store->datarooms->count())
                                    @foreach ($store->datarooms as $data_room)
                                    <div class="data-room-item card mb-3">
                                        <div class="card-body">
                                          <div class="row">
                                            <!-- Left section with product label -->
                                            <div class="col-lg-auto">
                                              <div class="d-flex justify-content-between">
                                                <span class="badge bg-light text-dark p-3">{{ $data_room->category }}</span>
                                                <button class="btn btn-link d-lg-none p-0">
                                                  <i class="fas fa-arrow-down-to-bracket"></i>
                                                </button>
                                              </div>
                                            </div>

                                            <!-- Middle section with product details -->
                                            <div class="col">
                                              <h5 class="card-title">{{ $data_room->title }}</h5>
                                              <div class="d-flex flex-wrap align-items-center text-muted">
                                                <span>{{ $store->name }}</span>
                                                {{-- <div class="vr mx-2"></div>
                                                <span>CNC Wire Cutting Machine Numacut</span> --}}
                                              </div>
                                            </div>

                                            <!-- Right section with download button (visible only on large screens) -->
                                            <div class="col-lg-auto d-none d-lg-flex align-items-center">
                                              <a href="{{ RvMedia::getImageUrl($data_room->file) }}" target="_blank" class="ps-btn ps-btn--outline">
                                                <span><svg style="height: 13px;width: 20px;margin-top: -5px;" aria-hidden="true" focusable="false" data-prefix="far" data-icon="arrow-down-to-bracket" class="svg-inline--fa fa-arrow-down-to-bracket w-3.5 h-3.5 text-body2" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512"><path fill="currentColor" d="M352 512H96c-53.02 0-96-42.98-96-96v-72C0 330.7 10.75 320 24 320c13.26 0 24 10.75 24 24V416c0 26.4 21.6 48 48 48h256c26.4 0 48-21.6 48-48v-72c0-13.25 10.75-24 24-24c13.26 0 24 10.75 24 24V416C448 469 405 512 352 512zM240.4 345.5l136-128c9.688-9.094 10.12-24.28 1.031-33.91c-9.062-9.656-24.25-10.12-33.91-1.031L248 272.4V24C248 10.75 237.3 0 224 0S200 10.75 200 24v248.4L104.4 182.5C94.78 173.4 79.59 173.9 70.53 183.6C66.16 188.2 64 194.1 64 200c0 6.375 2.531 12.75 7.562 17.47l136 128C216.8 354.2 231.2 354.2 240.4 345.5z"></path></svg></span>
                                                Download
                                              </a>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                      @endforeach
                                    @else
                                        <p>{{ __('No Data Room Available') }}</p>
                                    @endif
                                </div>
                                <div class="ps-tab" id="tab-4">
                                    @if ($store->distributors->count())


                                    <div class="row">
                                        @foreach ($store->distributors as $distributor)
                                        <div class="col-md-4">
                                            <div class="distributor-card">
                                                <div class="d-flex justify-content-between">
                                                    <div>
                                                        <div class="distributor-card-flag">
                                                            <img src="https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.4.3/flags/4x3/{{ strtolower($distributor->country) }}.svg">
                                                        </div>
                                                        <div class="distributor-name">{{ $distributor->name }}</div>
                                                        <div class="d-flex">
                                                            <div class="distributor-cat me-3">
                                                                <img src="{{ RvMedia::getImageUrl($distributor->category->icon_image) }}">
                                                                <span>{{ $distributor->category->name }}</span>
                                                            </div>
                                                            <div class="distributor-brand">
                                                                <img src="{{ RvMedia::getImageUrl($distributor->brand->logo) }}">
                                                                <span>{{ $distributor->brand->name }}</span>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="distributor-logo">
                                                        <img src="{{ RvMedia::getImageUrl($distributor->logo, 'thumb', false, RvMedia::getDefaultImage()) }}" alt="{{ $distributor->name }}">
                                                    </div>
                                                </div>
                                                <div class="divider"></div>
                                                <a href="{{ $distributor->website }}" class="contact-info">
                                                    <i class="fas fa-home"></i>
                                                    {{ $distributor->website }}
                                                </a>
                                                <a href="tel:+97313114133" class="contact-info">
                                                    <i class="fas fa-phone"></i>
                                                    {{ $distributor->phone }}
                                                </a>
                                                <a href="mailto:{{ $distributor->email }}" class="contact-info">
                                                    <i class="fas fa-envelope"></i>
                                                    {{ $distributor->email }}
                                                </a>

                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                    @else
                                        <p>{{ __('No Distributor Available') }}</p>
                                    @endif
                                </div>
                                <div class="ps-tab" id="tab-5">

                                    {{-- <p><strong> {{ $products->total() }}</strong> {{ __('Products found') }}</p> --}}
                                    <div class="ps-shopping-product">
                                        @if ($products->isNotEmpty())
                                            @foreach ($products as $product)
                                            <div class="card mb-3">
                                                <div class="card-body">
                                                    <div class="row">
                                                        <div class="col-lg-auto">
                                                            <a href="{{ $product->url }}">
                                                                @if ($product->models->count())
                                                                    @foreach($product->models as $model)
                                                                        <span class="text-dark p-3 fs-3">{{ $model->name }}</span> @if (!$loop->last),@endif
                                                                    @endforeach
                                                                @endif
                                                            </a>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            @endforeach
                                        @endif
                                    </div>
                                    <div class="ps-pagination">
                                        {!! $products->withQueryString()->links() !!}
                                    </div>
                                </div>

                    </div>
                </div>
            </div>
            {{-- @if ($canContactStore)
        </div>
        @endif --}}
</div>
</div>
</section>
</div>
