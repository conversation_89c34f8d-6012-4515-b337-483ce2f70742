{"__meta": {"id": "01JXNG1DD4PGGGXMNX4GTSF0FW", "datetime": "2025-06-13 20:34:58", "utime": **********.0853, "method": "POST", "uri": "/admin/ecommerce/products?", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.147261, "end": **********.085312, "duration": 0.9380509853363037, "duration_str": "938ms", "measures": [{"label": "Booting", "start": **********.147261, "relative_start": 0, "end": **********.822384, "relative_end": **********.822384, "duration": 0.****************, "duration_str": "675ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.822394, "relative_start": 0.****************, "end": **********.085314, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "263ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.840849, "relative_start": 0.****************, "end": **********.848401, "relative_end": **********.848401, "duration": 0.007552146911621094, "duration_str": "7.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: core/table::bulk-changes", "start": **********.899639, "relative_start": 0.***************, "end": **********.899639, "relative_end": **********.899639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.901925, "relative_start": 0.****************, "end": **********.901925, "relative_end": **********.901925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3952f2f8079cae92614235bd0a03d56", "start": **********.904233, "relative_start": 0.756972074508667, "end": **********.904233, "relative_end": **********.904233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.904503, "relative_start": 0.7572422027587891, "end": **********.904503, "relative_end": **********.904503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.904993, "relative_start": 0.7577321529388428, "end": **********.904993, "relative_end": **********.904993, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.90544, "relative_start": 0.7581791877746582, "end": **********.90544, "relative_end": **********.90544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.905864, "relative_start": 0.7586030960083008, "end": **********.905864, "relative_end": **********.905864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.906332, "relative_start": 0.7590711116790771, "end": **********.906332, "relative_end": **********.906332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.906808, "relative_start": 0.7595469951629639, "end": **********.906808, "relative_end": **********.906808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "start": **********.907233, "relative_start": 0.759972095489502, "end": **********.907233, "relative_end": **********.907233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.create", "start": **********.912591, "relative_start": 0.7653300762176514, "end": **********.912591, "relative_end": **********.912591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::21a9e4f6a6bd57aad5711446b399a39c", "start": **********.913404, "relative_start": 0.7661430835723877, "end": **********.913404, "relative_end": **********.913404, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::includes.header-action", "start": **********.914328, "relative_start": 0.7670671939849854, "end": **********.914328, "relative_end": **********.914328, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::60cb3c84e43d8fd13d066b6332a243d5", "start": **********.915075, "relative_start": 0.7678141593933105, "end": **********.915075, "relative_end": **********.915075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::includes.header-action", "start": **********.915389, "relative_start": 0.7681281566619873, "end": **********.915389, "relative_end": **********.915389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::5d51c9333cac7b48f699a15fa5950d60", "start": **********.916046, "relative_start": 0.7687849998474121, "end": **********.916046, "relative_end": **********.916046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::table-info", "start": **********.917152, "relative_start": 0.7698910236358643, "end": **********.917152, "relative_end": **********.917152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::0343a1b0800146d7d9cf6a9514ec7bf4", "start": **********.917853, "relative_start": 0.770592212677002, "end": **********.917853, "relative_end": **********.917853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: a74ad8dfacd4f985eb3977517615ce25::badge", "start": **********.918124, "relative_start": 0.7708630561828613, "end": **********.918124, "relative_end": **********.918124, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::row-actions", "start": **********.033587, "relative_start": 0.8863260746002197, "end": **********.033587, "relative_end": **********.033587, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.03415, "relative_start": 0.8868889808654785, "end": **********.03415, "relative_end": **********.03415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.034475, "relative_start": 0.887214183807373, "end": **********.034475, "relative_end": **********.034475, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.035107, "relative_start": 0.8878459930419922, "end": **********.035107, "relative_end": **********.035107, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::956251f2dec11412ac25a26a9f7d71fa", "start": **********.035985, "relative_start": 0.8887240886688232, "end": **********.035985, "relative_end": **********.035985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.action", "start": **********.03624, "relative_start": 0.8889791965484619, "end": **********.03624, "relative_end": **********.03624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-attributes", "start": **********.036483, "relative_start": 0.8892221450805664, "end": **********.036483, "relative_end": **********.036483, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::actions.includes.action-icon", "start": **********.037016, "relative_start": 0.8897550106048584, "end": **********.037016, "relative_end": **********.037016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::151f0fb0c0a10839cb1d6b3ad5c9290f", "start": **********.037765, "relative_start": 0.8905041217803955, "end": **********.037765, "relative_end": **********.037765, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/language-advanced::language-column", "start": **********.038865, "relative_start": 0.8916041851043701, "end": **********.038865, "relative_end": **********.038865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::ab1a4d173a14198db45d9196164e988b", "start": **********.04146, "relative_start": 0.8941991329193115, "end": **********.04146, "relative_end": **********.04146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::7acd10aaf307836f141c823026de3629", "start": **********.043745, "relative_start": 0.8964841365814209, "end": **********.043745, "relative_end": **********.043745, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: plugins/ecommerce::products.partials.sort-order", "start": **********.055212, "relative_start": 0.9079511165618896, "end": **********.055212, "relative_end": **********.055212, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: core/table::partials.checkbox", "start": **********.058169, "relative_start": 0.9109079837799072, "end": **********.058169, "relative_end": **********.058169, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.082325, "relative_start": 0.9350640773773193, "end": **********.08282, "relative_end": **********.08282, "duration": 0.0004949569702148438, "duration_str": "495μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 55516976, "peak_usage_str": "53MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.44.2", "PHP Version": "8.3.17", "Environment": "localhost", "Debug Mode": "Enabled", "URL": "muhrak.gc", "Timezone": "UTC", "Locale": "en"}}, "views": {"count": 33, "nb_templates": 33, "templates": [{"name": "core/table::bulk-changes", "param_count": null, "params": [], "start": **********.899614, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/bulk-changes.blade.phpcore/table::bulk-changes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fbulk-changes.blade.php&line=1", "ajax": false, "filename": "bulk-changes.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.901906, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "__components::b3952f2f8079cae92614235bd0a03d56", "param_count": null, "params": [], "start": **********.904213, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/b3952f2f8079cae92614235bd0a03d56.blade.php__components::b3952f2f8079cae92614235bd0a03d56", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fb3952f2f8079cae92614235bd0a03d56.blade.php&line=1", "ajax": false, "filename": "b3952f2f8079cae92614235bd0a03d56.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.904485, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.904965, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.90542, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.905845, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.906312, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.906789, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::dropdown.item", "param_count": null, "params": [], "start": **********.907214, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/dropdown/item.blade.phpa74ad8dfacd4f985eb3977517615ce25::dropdown.item", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fdropdown%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}}, {"name": "core/table::partials.create", "param_count": null, "params": [], "start": **********.912568, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/partials/create.blade.phpcore/table::partials.create", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcreate.blade.php&line=1", "ajax": false, "filename": "create.blade.php", "line": "?"}}, {"name": "__components::21a9e4f6a6bd57aad5711446b399a39c", "param_count": null, "params": [], "start": **********.913384, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/21a9e4f6a6bd57aad5711446b399a39c.blade.php__components::21a9e4f6a6bd57aad5711446b399a39c", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F21a9e4f6a6bd57aad5711446b399a39c.blade.php&line=1", "ajax": false, "filename": "21a9e4f6a6bd57aad5711446b399a39c.blade.php", "line": "?"}}, {"name": "core/table::includes.header-action", "param_count": null, "params": [], "start": **********.914309, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/includes/header-action.blade.phpcore/table::includes.header-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fincludes%2Fheader-action.blade.php&line=1", "ajax": false, "filename": "header-action.blade.php", "line": "?"}}, {"name": "__components::60cb3c84e43d8fd13d066b6332a243d5", "param_count": null, "params": [], "start": **********.915056, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/60cb3c84e43d8fd13d066b6332a243d5.blade.php__components::60cb3c84e43d8fd13d066b6332a243d5", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F60cb3c84e43d8fd13d066b6332a243d5.blade.php&line=1", "ajax": false, "filename": "60cb3c84e43d8fd13d066b6332a243d5.blade.php", "line": "?"}}, {"name": "core/table::includes.header-action", "param_count": null, "params": [], "start": **********.915369, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/includes/header-action.blade.phpcore/table::includes.header-action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fincludes%2Fheader-action.blade.php&line=1", "ajax": false, "filename": "header-action.blade.php", "line": "?"}}, {"name": "__components::5d51c9333cac7b48f699a15fa5950d60", "param_count": null, "params": [], "start": **********.916027, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/5d51c9333cac7b48f699a15fa5950d60.blade.php__components::5d51c9333cac7b48f699a15fa5950d60", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F5d51c9333cac7b48f699a15fa5950d60.blade.php&line=1", "ajax": false, "filename": "5d51c9333cac7b48f699a15fa5950d60.blade.php", "line": "?"}}, {"name": "core/table::table-info", "param_count": null, "params": [], "start": **********.917132, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/table-info.blade.phpcore/table::table-info", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Ftable-info.blade.php&line=1", "ajax": false, "filename": "table-info.blade.php", "line": "?"}}, {"name": "__components::0343a1b0800146d7d9cf6a9514ec7bf4", "param_count": null, "params": [], "start": **********.917834, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/0343a1b0800146d7d9cf6a9514ec7bf4.blade.php__components::0343a1b0800146d7d9cf6a9514ec7bf4", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F0343a1b0800146d7d9cf6a9514ec7bf4.blade.php&line=1", "ajax": false, "filename": "0343a1b0800146d7d9cf6a9514ec7bf4.blade.php", "line": "?"}}, {"name": "a74ad8dfacd4f985eb3977517615ce25::badge", "param_count": null, "params": [], "start": **********.918105, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/base/resources/views/components/badge.blade.phpa74ad8dfacd4f985eb3977517615ce25::badge", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fresources%2Fviews%2Fcomponents%2Fbadge.blade.php&line=1", "ajax": false, "filename": "badge.blade.php", "line": "?"}}, {"name": "core/table::row-actions", "param_count": null, "params": [], "start": **********.033565, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/row-actions.blade.phpcore/table::row-actions", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Frow-actions.blade.php&line=1", "ajax": false, "filename": "row-actions.blade.php", "line": "?"}}, {"name": "core/table::actions.action", "param_count": null, "params": [], "start": **********.034131, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/action.blade.phpcore/table::actions.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}}, {"name": "core/table::actions.includes.action-attributes", "param_count": null, "params": [], "start": **********.034456, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/includes/action-attributes.blade.phpcore/table::actions.includes.action-attributes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-attributes.blade.php&line=1", "ajax": false, "filename": "action-attributes.blade.php", "line": "?"}}, {"name": "core/table::actions.includes.action-icon", "param_count": null, "params": [], "start": **********.035086, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/includes/action-icon.blade.phpcore/table::actions.includes.action-icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-icon.blade.php&line=1", "ajax": false, "filename": "action-icon.blade.php", "line": "?"}}, {"name": "__components::956251f2dec11412ac25a26a9f7d71fa", "param_count": null, "params": [], "start": **********.035966, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/956251f2dec11412ac25a26a9f7d71fa.blade.php__components::956251f2dec11412ac25a26a9f7d71fa", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F956251f2dec11412ac25a26a9f7d71fa.blade.php&line=1", "ajax": false, "filename": "956251f2dec11412ac25a26a9f7d71fa.blade.php", "line": "?"}}, {"name": "core/table::actions.action", "param_count": null, "params": [], "start": **********.036221, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/action.blade.phpcore/table::actions.action", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Faction.blade.php&line=1", "ajax": false, "filename": "action.blade.php", "line": "?"}}, {"name": "core/table::actions.includes.action-attributes", "param_count": null, "params": [], "start": **********.036464, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/includes/action-attributes.blade.phpcore/table::actions.includes.action-attributes", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-attributes.blade.php&line=1", "ajax": false, "filename": "action-attributes.blade.php", "line": "?"}}, {"name": "core/table::actions.includes.action-icon", "param_count": null, "params": [], "start": **********.036988, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/actions/includes/action-icon.blade.phpcore/table::actions.includes.action-icon", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Factions%2Fincludes%2Faction-icon.blade.php&line=1", "ajax": false, "filename": "action-icon.blade.php", "line": "?"}}, {"name": "__components::151f0fb0c0a10839cb1d6b3ad5c9290f", "param_count": null, "params": [], "start": **********.037746, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/151f0fb0c0a10839cb1d6b3ad5c9290f.blade.php__components::151f0fb0c0a10839cb1d6b3ad5c9290f", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F151f0fb0c0a10839cb1d6b3ad5c9290f.blade.php&line=1", "ajax": false, "filename": "151f0fb0c0a10839cb1d6b3ad5c9290f.blade.php", "line": "?"}}, {"name": "plugins/language-advanced::language-column", "param_count": null, "params": [], "start": **********.038844, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/language-advanced/resources/views/language-column.blade.phpplugins/language-advanced::language-column", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage-advanced%2Fresources%2Fviews%2Flanguage-column.blade.php&line=1", "ajax": false, "filename": "language-column.blade.php", "line": "?"}}, {"name": "__components::ab1a4d173a14198db45d9196164e988b", "param_count": null, "params": [], "start": **********.04144, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/ab1a4d173a14198db45d9196164e988b.blade.php__components::ab1a4d173a14198db45d9196164e988b", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2Fab1a4d173a14198db45d9196164e988b.blade.php&line=1", "ajax": false, "filename": "ab1a4d173a14198db45d9196164e988b.blade.php", "line": "?"}}, {"name": "__components::7acd10aaf307836f141c823026de3629", "param_count": null, "params": [], "start": **********.043725, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\storage\\framework\\views/7acd10aaf307836f141c823026de3629.blade.php__components::7acd10aaf307836f141c823026de3629", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fstorage%2Fframework%2Fviews%2F7acd10aaf307836f141c823026de3629.blade.php&line=1", "ajax": false, "filename": "7acd10aaf307836f141c823026de3629.blade.php", "line": "?"}}, {"name": "plugins/ecommerce::products.partials.sort-order", "param_count": null, "params": [], "start": **********.055189, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/plugins/ecommerce/resources/views/products/partials/sort-order.blade.phpplugins/ecommerce::products.partials.sort-order", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fresources%2Fviews%2Fproducts%2Fpartials%2Fsort-order.blade.php&line=1", "ajax": false, "filename": "sort-order.blade.php", "line": "?"}}, {"name": "core/table::partials.checkbox", "param_count": null, "params": [], "start": **********.058148, "type": "blade", "hash": "bladeD:\\laragon\\www\\muhrak\\platform/core/table/resources/views/partials/checkbox.blade.phpcore/table::partials.checkbox", "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Ftable%2Fresources%2Fviews%2Fpartials%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}]}, "queries": {"count": 12, "nb_statements": 12, "nb_visible_statements": 12, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06052, "accumulated_duration_str": "60.52ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 170}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "D:\\laragon\\www\\muhrak\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 83}], "start": **********.860867, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0, "width_percent": 0.777}, {"sql": "select `lang_locale`, `lang_code`, `lang_name`, `lang_flag`, `lang_is_rtl` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 105}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/base/src/Http/Middleware/AdminLocaleMiddleware.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Http\\Middleware\\AdminLocaleMiddleware.php", "line": 28}], "start": **********.867522, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 0.777, "width_percent": 0.661}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 230}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 24, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 275}], "start": **********.9197779, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 1.438, "width_percent": 0.661}, {"sql": "select `lang_code`, `lang_name`, `lang_flag` from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 17, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 1184}, {"index": 19, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 238}, {"index": 23, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}], "start": **********.92237, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.098, "width_percent": 0.512}, {"sql": "select * from `languages` order by `lang_order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/language/src/LanguageManager.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\LanguageManager.php", "line": 156}, {"index": 18, "namespace": null, "name": "platform/plugins/language/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language\\src\\Providers\\HookServiceProvider.php", "line": 81}, {"index": 22, "namespace": null, "name": "platform/core/base/helpers/action-filter.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\helpers\\action-filter.php", "line": 39}, {"index": 26, "namespace": null, "name": "platform/plugins/language-advanced/src/Providers/HookServiceProvider.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\language-advanced\\src\\Providers\\HookServiceProvider.php", "line": 238}], "start": **********.924644, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 2.611, "width_percent": 0.711}, {"sql": "select count(*) as aggregate from `ec_products` where `is_variation` = 0", "type": "query", "params": [], "bindings": [0], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 707}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 148}, {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 20, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}], "start": **********.9367552, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:165", "source": {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=165", "ajax": false, "filename": "QueryDataTable.php", "line": "165"}, "connection": "muhrak", "explain": null, "start_percent": 3.321, "width_percent": 0.777}, {"sql": "select count(*) as aggregate from `ec_products` where `is_variation` = 0 and (`ec_products`.`name` LIKE '%COOLANT PUMP REAR HOUSING%' and `is_variation` = 0) or (`is_variation` = 0 and (`ec_products`.`sku` LIKE '%COOLANT PUMP REAR HOUSING%' or `ec_products`.`created_at` LIKE '%COOLANT PUMP REAR HOUSING%' or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `name` LIKE '%COOLANT PUMP REAR HOUSING%') or exists (select * from `ec_product_variations` where `ec_products`.`id` = `ec_product_variations`.`configurable_product_id` and exists (select * from `ec_products` where `ec_product_variations`.`product_id` = `ec_products`.`id` and `sku` LIKE '%COOLANT PUMP REAR HOUSING%'))))", "type": "query", "params": [], "bindings": [0, "%COOLANT PUMP REAR HOUSING%", 0, 0, "%COOLANT PUMP REAR HOUSING%", "%COOLANT PUMP REAR HOUSING%", "%COOLANT PUMP REAR HOUSING%", "%COOLANT PUMP REAR HOUSING%"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/DataTableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\DataTableAbstract.php", "line": 784}, {"index": 18, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 259}, {"index": 19, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 150}, {"index": 20, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}], "start": **********.945868, "duration": 0.022690000000000002, "duration_str": "22.69ms", "memory": 0, "memory_str": null, "filename": "QueryDataTable.php:165", "source": {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 165}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fvendor%2Fyajra%2Flaravel-datatables-oracle%2Fsrc%2FQueryDataTable.php&line=165", "ajax": false, "filename": "QueryDataTable.php", "line": "165"}, "connection": "muhrak", "explain": null, "start_percent": 4.098, "width_percent": 37.492}, {"sql": "select `id`, `name`, `order`, `created_at`, `status`, `sku`, `image`, `images`, `price`, `sale_price`, `sale_type`, `start_date`, `end_date`, `quantity`, `with_storehouse_management`, `stock_status`, `product_type`, `ec_products`.`store_id` from `ec_products` where `is_variation` = 0 and (`ec_products`.`name` LIKE '%COOLANT PUMP REAR HOUSING%' and `is_variation` = 0) or (`is_variation` = 0 and (`ec_products`.`sku` LIKE '%COOLANT PUMP REAR HOUSING%' or `ec_products`.`created_at` LIKE '%COOLANT PUMP REAR HOUSING%' or exists (select * from `mp_stores` where `ec_products`.`store_id` = `mp_stores`.`id` and `name` LIKE '%COOLANT PUMP REAR HOUSING%') or exists (select * from `ec_product_variations` where `ec_products`.`id` = `ec_product_variations`.`configurable_product_id` and exists (select * from `ec_products` where `ec_product_variations`.`product_id` = `ec_products`.`id` and `sku` LIKE '%COOLANT PUMP REAR HOUSING%')))) order by `id` desc limit 10 offset 0", "type": "query", "params": [], "bindings": [0, "%COOLANT PUMP REAR HOUSING%", 0, 0, "%COOLANT PUMP REAR HOUSING%", "%COOLANT PUMP REAR HOUSING%", "%COOLANT PUMP REAR HOUSING%", "%COOLANT PUMP REAR HOUSING%"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 137}, {"index": 17, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 18, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Tables/ProductTable.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Tables\\ProductTable.php", "line": 204}], "start": **********.971118, "duration": 0.03322, "duration_str": "33.22ms", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 41.59, "width_percent": 54.891}, {"sql": "select * from `mp_stores` where `mp_stores`.`id` in (0)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 21, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 22, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 137}, {"index": 23, "namespace": null, "name": "vendor/yajra/laravel-datatables-oracle/src/QueryDataTable.php", "file": "D:\\laragon\\www\\muhrak\\vendor\\yajra\\laravel-datatables-oracle\\src\\QueryDataTable.php", "line": 120}, {"index": 24, "namespace": null, "name": "platform/core/table/src/Abstracts/TableAbstract.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\table\\src\\Abstracts\\TableAbstract.php", "line": 918}], "start": **********.005642, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 96.481, "width_percent": 0.711}, {"sql": "select * from `ec_flash_sales` where date(`end_date`) >= '2025-06-13' and `status` = 'published' order by `created_at` desc", "type": "query", "params": [], "bindings": ["2025-06-13", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/FlashSaleRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\FlashSaleRepository.php", "line": 25}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 52}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/FlashSaleSupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\FlashSaleSupport.php", "line": 22}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 46}], "start": **********.020413, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 97.191, "width_percent": 0.892}, {"sql": "select * from `ec_discounts` where `type` = 'promotion' and `start_date` <= '2025-06-13 20:34:58' and (`end_date` is null or `end_date` >= '2025-06-13 20:34:58') and (`target` in ('all-orders', 'amount-minimum-order') or (`target` in ('customer', 'group-products', 'products-by-category', 'specific-product', 'product-variant') and `product_quantity` = 1))", "type": "query", "params": [], "bindings": ["promotion", "2025-06-13 20:34:58", "2025-06-13 20:34:58", "all-orders", "amount-minimum-order", "customer", "group-products", "products-by-category", "specific-product", "product-variant", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Repositories/Eloquent/DiscountRepository.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Repositories\\Eloquent\\DiscountRepository.php", "line": 50}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 116}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/DiscountSupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\DiscountSupport.php", "line": 42}, {"index": 19, "namespace": null, "name": "platform/plugins/ecommerce/src/Models/Concerns/ProductPrices.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Models\\Concerns\\ProductPrices.php", "line": 58}], "start": **********.027025, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 98.083, "width_percent": 1.157}, {"sql": "select * from `ec_currencies` order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, {"index": 16, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 107}, {"index": 17, "namespace": null, "name": "platform/plugins/ecommerce/src/Supports/CurrencySupport.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\src\\Supports\\CurrencySupport.php", "line": 41}, {"index": 18, "namespace": null, "name": "platform/plugins/ecommerce/helpers/currencies.php", "file": "D:\\laragon\\www\\muhrak\\platform\\plugins\\ecommerce\\helpers\\currencies.php", "line": 141}, {"index": 27, "namespace": null, "name": "platform/core/base/src/Models/BaseModel.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseModel.php", "line": 28}], "start": **********.049443, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "BaseQueryBuilder.php:48", "source": {"index": 15, "namespace": null, "name": "platform/core/base/src/Models/BaseQueryBuilder.php", "file": "D:\\laragon\\www\\muhrak\\platform\\core\\base\\src\\Models\\BaseQueryBuilder.php", "line": 48}, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Fbase%2Fsrc%2FModels%2FBaseQueryBuilder.php&line=48", "ajax": false, "filename": "BaseQueryBuilder.php", "line": "48"}, "connection": "muhrak", "explain": null, "start_percent": 99.24, "width_percent": 0.76}]}, "models": {"data": {"Botble\\Language\\Models\\Language": {"value": 8, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Flanguage%2Fsrc%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Currency": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FCurrency.php&line=1", "ajax": false, "filename": "Currency.php", "line": "?"}}, "Botble\\ACL\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fcore%2Facl%2Fsrc%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Botble\\Ecommerce\\Models\\Product": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}}, "count": 14, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/ecommerce/products", "action_name": "products.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@index", "uri": "GET admin/ecommerce/products", "controller": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@index<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=37\" class=\"phpdebugbar-widgets-editor-link\"></a>", "namespace": "Botble\\Ecommerce\\Http\\Controllers", "prefix": "admin/ecommerce/products", "file": "<a href=\"phpstorm://open?file=D%3A%2Flaragon%2Fwww%2Fmuhrak%2Fplatform%2Fplugins%2Fecommerce%2Fsrc%2FHttp%2FControllers%2FProductController.php&line=37\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">platform/plugins/ecommerce/src/Http/Controllers/ProductController.php:37-45</a>", "middleware": "web, core, auth", "duration": "935ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1867041808 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1867041808\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-610349619 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>draw</span>\" => \"<span class=sf-dump-str title=\"2 characters\">14</span>\"\n  \"<span class=sf-dump-key>columns</span>\" => <span class=sf-dump-note>array:14</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">checkbox</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">price</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock_status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"12 characters\">stock_status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">quantity</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">sku</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">order</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">created_at</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">store_id</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"8 characters\">store_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"8 characters\">language</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"26 characters\">language_meta.lang_meta_id</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n    <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>data</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">row_actions</span>\"\n      \"<span class=sf-dump-key>searchable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>orderable</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>value</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>column</span>\" => \"<span class=sf-dump-str>1</span>\"\n      \"<span class=sf-dump-key>dir</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>start</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>length</span>\" => \"<span class=sf-dump-str title=\"2 characters\">10</span>\"\n  \"<span class=sf-dump-key>search</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>value</span>\" => \"<span class=sf-dump-str title=\"25 characters\">COOLANT PUMP REAR HOUSING</span>\"\n    \"<span class=sf-dump-key>regex</span>\" => \"<span class=sf-dump-str title=\"5 characters\">false</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-610349619\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2050381411 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">3274</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Google Chrome&quot;;v=&quot;137&quot;, &quot;Chromium&quot;;v=&quot;137&quot;, &quot;Not/A)Brand&quot;;v=&quot;24&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"46 characters\">application/json, text/javascript, */*; q=0.01</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">https://muhrak.gc</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"42 characters\">https://muhrak.gc/admin/ecommerce/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"50 characters\">en-US,en;q=0.9,ur;q=0.8,pl;q=0.7,ru;q=0.6,ar;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3140 characters\">cookie_for_consent=1; botble_footprints_cookie=eyJpdiI6InJtenpwcG01ZldyRCs2TFV4YUYrYUE9PSIsInZhbHVlIjoiamVKQ2MwT2VxdDI3L0pXTXdlMURsY0JJSHdaWVp5T1VoSUNSbEpIZ0pZcHdKWGZId3lBLzJ2b1pIUXVYUUxKRjdMY3k5Q1VpTWRFd3Ftbzd6L3UyeHUvdWFpdTAwUXBNZER4MDc5MGJjOXQxQXhIazNiaTI0TU42YXJkbHNVek8iLCJtYWMiOiI3YWM1Njc0ODBhMzFhMjBlOWM2NzQ2YTE1ODRkNGM0ODk5YTNhYjE5ZjliNjY3NGI2MzA5MjMxNGJhNDliZjJjIiwidGFnIjoiIn0%3D; botble_footprints_cookie_data=eyJpdiI6ImozUjJ6OXN5RkhxbXdCYmc5K1JURlE9PSIsInZhbHVlIjoib3plTVp0NnFEZVhoU1NQejZBMklhY2RqSEtwTmNvaG1SeWtpbFkvZmVZb21QUmg4VTFvRVlPbUUzd1NGQ3J6dFZVT0hmdDIrTWd0SGF1Y28yYWx1VWJYdTJ0OUFvdkxJM3U3UktZNXpBL1pReU8xcDI0S3AxSmRkVzVHaHhyZHlia1ptb0wrTUhkNHRPbXhoY2pNL2JoZ2JXVGNTaHI2VjRIRW1HOUdiVHAwN2xxdkJLazdwZDFadCtJaTNlTk9HT2JmWms2Vnp5bGorWkxFd1dvaStjUHlEY1ZQR2tYOW5tREUxV25Vek1jbDFkenJlZENXR3VIaHM4aFdLaDRVNjViY21idXByU3ZQQVFCU2gxcVNnYW5zbERUamgxZjNUMVRIWEt4V0tUbGxFRDc5VzhDNkZRZ0g1QXRjU3Rna3ZTQlRwZjVScFM1eEdzUmNhSlAxaHpIZ2kxdVEvOG51K1FlVmhuSTBGdUZxYXdkdGdWLzFDdW1nZ0MvdDFnTit6STdZd3JJWnVCNGZZSTNFRWtjaTJndzZVT3prNEkrb1c0cTFFaGFWckxiUUtVSFVsYXlWSWxnUSttR2dxYlo0K0dCY1NzRDBGdzVNZGdLTnFOZG43VllRTkFORnIrZlk1SEt3ZFkvNjYwdjJFWmFJaCtPUVh4TjFZejFleUQvU09oeERvVkdyVVJ2d0ZkM25TZXU1OGNZOE5PWTJWVmV4WWpsWUE2Zmc0dHV3PSIsIm1hYyI6IjYwMTc0ODhiZDNjN2VkZGVmZjliNmYyNWU0ZTU2ZWE5YjhkMjllNzk1ZDAxNGU2ZWU3MDhkOTA3NzM3OTFjNjUiLCJ0YWciOiIifQ%3D%3D; remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6IktsRlR4UnBrM0szS1Y0SUxrVVlUdHc9PSIsInZhbHVlIjoibVgzWjBpSHR4T2o4NlJBc3ZWNEVGZGVXS0xzU3NlNjRPOXFyNVFIOVVkbCt1ODRxbVZFNkNaODlSa3BORzF3bTJEb3Rkb0RVTVVjZHNBVFZiVnQ2TlhLOVBNVkJhVUIzb0ZZV2VTOER2dEJjd1ZZZEw3OWg4UEV4OXZqSjQwR2hsbERhOTdCQ1puTTJGRG1nMnozejdpcXp0NllsWUVzYWl5SjFlZGhYVWdUNmYyY2xRVXdXeG9UU3M3S1kxMnVxbVc5bktlTHFEOHNJbDlUV2JPamxrK3AwangreXlUMmtBTVhRdlN0SHQwcz0iLCJtYWMiOiIzNTliMzJjOTQxODc2NDdkNDI0YTBiNjZkMzUzZjEzNzZjYWMwYzlmNDQ2NGZhZjJiOTk5M2NhYTc0YmEyOGY5IiwidGFnIjoiIn0%3D; remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6Ijl6MUxxZXpoME96cm9sVGNGeUlyaEE9PSIsInZhbHVlIjoiWlR1dnBBRnpBZWF2c1I3dndid2M5UllhSUVPN0Fqa1p0K0RGelpiVkRpditYckx0YWZ2RVZFbTMwUGVnVGh4ODc5VFBseUtFaytWNWpRalB1QVhJbHZONFhqaFc2UjdjTlI1SlR0TDAwMS9ldmZxN1F5aTZpaWdnaFNOaGs0VmRWMlBoNkhMeTZKSU1hVTA3UHRKNUJKb3hPUEJVUWtEcjc2RFZmQ3NVanJEeHNHMU1jc3QzQ2N5blJKUUJVaFNsUEFQSmVKSHBiTjhtVmU2OEUwY1dBMTNyblVPOENzcTlRcHBOOTVuTWc1OD0iLCJtYWMiOiJhNzJhNzEyNGRjOTM0ZmVhMTIyNDU0ZmM0Mzc2MDQzZjI3MjNjNmM4NTUwMGQ2ZTZlNmZjM2NlMWZkYTgyMGM0IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlZibDAzYWg0Mm5PdVlnTmhZQ3g4N0E9PSIsInZhbHVlIjoiVlR1dytIMjUwbVR3OWthbEhQalBnOG94YlRTTnRPYUlwRnJJZzRuaEd2Zkp4dkhRbXpETWFVQ2hWZ3RtcXcxZ3NiZXd1KzhBcUwrSWZ0d1pCaGZZUnROYVVLR29SUU9Wa01mOGFLSVBrNC9DdlhSMmV2dmY1UlVwREJjdTJCYUEiLCJtYWMiOiI1MzFjM2ZiZjZkMzhjZGNhMTNkNmNkODBmMGExMDI4MDM4ODE5ZTNmMTc0YzAwYWFjNzBlOTA1YWI2YTRkZDZiIiwidGFnIjoiIn0%3D; botble_session=eyJpdiI6IkNXR2F2U1RlcExoekJMZnBmTmxJWnc9PSIsInZhbHVlIjoiRktmYVp6YVcrKzBmSEd5czk2SHVLUHc2eVRaaGhUOTI0dmU4L2J3L2p2K1dMbncxVGNQY0hPSTZWa2lqZXJxbWI2UHhHRWhjT3A4SFJzSWE4aHBUUVpZTzgwWlBoRHZjZHptZGhBa2Zib3Z2UzVlQXdKN3pPZGNYLzYzS3B5U0kiLCJtYWMiOiJmN2EwMDE1YzgxMmU3ZDA2NWNlMzZiMmUxMjA3ODQwZmFiMGRjOGRkYzJkY2NmMzYxOTBlZDNhOGE3OTUwM2NlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2050381411\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-594806902 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cookie_for_consent</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>botble_footprints_cookie</span>\" => \"<span class=sf-dump-str title=\"40 characters\">14908b951300852119d2c46221b4b11a7e2b8107</span>\"\n  \"<span class=sf-dump-key>botble_footprints_cookie_data</span>\" => \"<span class=sf-dump-str title=\"359 characters\">{&quot;footprint&quot;:&quot;14908b951300852119d2c46221b4b11a7e2b8107&quot;,&quot;ip&quot;:&quot;127.0.0.1&quot;,&quot;landing_domain&quot;:&quot;muhrak.gc&quot;,&quot;landing_page&quot;:&quot;install\\/welcome&quot;,&quot;landing_params&quot;:null,&quot;referral&quot;:null,&quot;gclid&quot;:null,&quot;fclid&quot;:null,&quot;utm_source&quot;:null,&quot;utm_campaign&quot;:null,&quot;utm_medium&quot;:null,&quot;utm_term&quot;:null,&quot;utm_content&quot;:null,&quot;referrer_url&quot;:&quot;http:\\/\\/localhost\\/&quot;,&quot;referrer_domain&quot;:&quot;localhost&quot;}</span>\"\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">1|lEoydfVrZKIFerelBjYotnW4P2e0TuoqJSFCOOr89XMprC6ygbibdEjl3rvX|$2y$12$6oEMzkNhsgbeo4WDg72G9e2nWxuvMvozEUdkIQ0AfpRz3PJNQ7XzG</span>\"\n  \"<span class=sf-dump-key>remember_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => \"<span class=sf-dump-str title=\"123 characters\">2|eZ0awTe8bfQLceQTos5gvZ7R55kW3IiLBR997mfYDDJcPndE2TcWxwpqf39P|$2y$12$EU5h35Jju9igRCRES6rqCuE42l30uwJ94rSesc56rboRI0xQqYy/G</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>botble_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">NlmohzskXFfa1956yAEyoTgVOMjJqryOKOFtoCn1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-594806902\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 13 Jun 2025 20:34:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1676445429 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">56w3qOLQ2WezgBAffsP49hPOj3d80BP8siOqKnfZ</span>\"\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>language</span>\" => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"42 characters\">https://muhrak.gc/admin/ecommerce/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>login_customer_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>2</span>\n  \"<span class=sf-dump-key>locale_direction</span>\" => <span class=sf-dump-const>false</span>\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1676445429\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://muhrak.gc/admin/ecommerce/products", "action_name": "products.index", "controller_action": "Botble\\Ecommerce\\Http\\Controllers\\ProductController@index"}, "badge": null}}